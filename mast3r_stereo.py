"""
MASt3R-SLAM 简化重构版本
提供更简洁易读的建图接口
"""

import os
import shutil
import pickle
import traceback
import torch
import lietorch
import numpy as np
import cv2
import pathlib
from logger import logger
from mast3r_slam.config import config
from mast3r_slam.frame import Mode, Frame, create_frame
from mast3r_slam.tracker import FrameTracker
from mast3r_slam.evaluate import save_reconstruction
import mast3r_slam_backends

from mast3r_slam.mast3r_utils import (
    load_retriever,
    mast3r_inference_mono,
    mast3r_match_symmetric,
    mast3r_symmetric_inference,
    mast3r_estimate,
    transpose_pose
)
from mast3r_slam.lietorch_utils import matrix_to_Sim3
from fast_intrinsics_estimator import FastIntrinsicsEstimator
from mast3r_slam.visualization_utils import show
import mast3r_slam.matching as matching


class SimpleKeyframes:
    """简化版的关键帧管理器，使用dict存储"""
    def __init__(self, h=None, w=None, device='cuda'):
        self.h = h
        self.w = w
        self.device = device

        # 关键帧字典 {frame_id: frame}
        self.keyframes = {}

        # 相机位姿字典 {frame_id: T_WC_data}
        self.T_WC = {}

        # 关键帧ID列表，保持插入顺序
        self.frame_ids = []

        self.last_keyframe_id = None

        # 相机内参
        self.K = None

    def __len__(self):
        return len(self.keyframes)

    def __getitem__(self, frame_id):
        """通过frame_id获取关键帧"""
        frame = self.keyframes[frame_id]
        # 确保Frame有最新的内参
        if self.K is not None and frame.K is None:
            frame.K = self.K
        return frame

    def __setitem__(self, frame_id, value):
        """设置关键帧"""
        self.keyframes[frame_id] = value
        self.T_WC[frame_id] = value.T_WC.data.clone()
        if frame_id not in self.frame_ids:
            self.frame_ids.append(frame_id)
        self.last_keyframe_id = frame_id

    def append(self, frame):
        """添加新关键帧"""
        frame_id = frame.frame_id
        self.keyframes[frame_id] = frame
        self.T_WC[frame_id] = frame.T_WC.data.clone()
        if frame_id not in self.frame_ids:
            self.frame_ids.append(frame_id)
        self.last_keyframe_id = frame_id

    def pop_last(self):
        """删除最后一个关键帧"""
        if self.last_keyframe_id:
            _idx = self.frame_ids.index(self.last_keyframe_id)
            self.frame_ids.pop(_idx)
            del self.keyframes[self.last_keyframe_id]
            del self.T_WC[self.last_keyframe_id]
            self.last_keyframe_id = None

    def remove(self, frame_id):
        """删除指定frame_id的关键帧"""
        if frame_id in self.keyframes:
            del self.keyframes[frame_id]
            del self.T_WC[frame_id]
            self.frame_ids.remove(frame_id)
            return True
        return False

    def get_frame_by_index(self, idx):
        """通过索引获取关键帧（仅用于evaluate.py兼容）"""
        if 0 <= idx < len(self.frame_ids):
            frame_id = self.frame_ids[idx]
            frame = self.keyframes[frame_id]
            # 确保Frame有最新的内参
            if self.K is not None and frame.K is None:
                frame.K = self.K
            return frame
        return None

    def set_intrinsics(self, K):
        self.K = K

    @property
    def n(self):
        return len(self.keyframes)

    def num_frames(self):
        return len(self.keyframes)

    def last_keyframe(self):
        """获取最后一个关键帧"""
        if not self.frame_ids:
            return None
        last_frame_id = self.frame_ids[-1]
        return self.keyframes[last_frame_id]

    def update_T_WCs(self, T_WCs, frame_ids):
        """更新指定frame_ids的相机位姿"""
        for i, frame_id in enumerate(frame_ids):
            if frame_id in self.keyframes:
                self.keyframes[frame_id].T_WC = lietorch.Sim3(T_WCs[i].data)
                self.T_WC[frame_id] = T_WCs[i].data.clone()

    def __iter__(self):
        """迭代器，按插入顺序返回关键帧"""
        for frame_id in self.frame_ids:
            frame = self.keyframes[frame_id]
            # 确保Frame有最新的内参
            if self.K is not None and frame.K is None:
                frame.K = self.K
            yield frame


class Mast3rStereo:
    """
    简化的MASt3R-SLAM建图类
    
    核心组件：
    - keyframes: 关键帧管理器
    - factor_graph: 因子图优化器
    - tracker: 帧跟踪器
    - retriever: 检索数据库
    """
    
    def __init__(self, model, device='cuda', K=None, workdir=None, save_checkpoint=True, start_pose=None, global_index=0):
        """
        初始化Mast3rStereo类
        
        Args:
            model: MASt3R模型
            h, w: 图像高度和宽度
            device: 计算设备
            K: 相机内参矩阵
        """
        self.model = model
        self.device = device
        self.K = K
        self.workdir = workdir
        self.save_checkpoint = save_checkpoint
        self.start_pose = start_pose
        self.global_index = global_index
        
        # 初始化核心组件
        self.keyframes = SimpleKeyframes(device)
        if K is not None:
            self.keyframes.set_intrinsics(K)
        self.temp_ids = []
            
        self.factor_graph = FactorGraph(model, self.keyframes, K, device)
        self.tracker = FrameTracker(model, self.keyframes, device)
        self.retriever = load_retriever(model, device=device)
        
        # 状态管理
        self._mode = Mode.INIT
        self._current_frame = None
        self.global_optimizer_tasks = []
        
        logger.info(f"Initialized Mast3rStereo with device={device}, K={'provided' if K is not None else 'None'}")

    def load(self, savedir=None):
        """恢复状态"""
        if savedir is None:
            savedir = self.workdir
        if savedir is None:
            return
        
        logger.info(f"Loading from {savedir}...")
        
        try:
            # load keyframes - 按frame_id排序确保正确顺序
            keyframe_dir = f"{savedir}/keyframes"
            if os.path.exists(keyframe_dir):
                frame_files = [f for f in os.listdir(keyframe_dir) if f.endswith('.pkl')]
                # 按frame_id排序
                frame_files.sort(key=lambda x: int(x.split('.')[0]))
                
                for frame_file in frame_files:
                    try:
                        with open(os.path.join(keyframe_dir, frame_file), 'rb') as f:
                            frame = pickle.load(f)
                            # 确保frame数据在正确设备上
                            if hasattr(frame, 'T_WC'):
                                frame.T_WC = frame.T_WC.to(self.device)
                            self.keyframes.append(frame)
                    except Exception as e:
                        logger.warning(f"Failed to load keyframe {frame_file}: {e}")
                        logger.error(f"Traceback: {traceback.format_exc()}")

            # load factor graph
            factor_graph_file = f"{savedir}/factor_graph.pkl"
            if os.path.exists(factor_graph_file):
                try:
                    with open(factor_graph_file, 'rb') as f:
                        factor_graph_data = pickle.load(f)
                        # 确保所有tensor都在正确设备上
                        self.factor_graph.ii = torch.from_numpy(factor_graph_data['ii']).to(self.device)
                        self.factor_graph.jj = torch.from_numpy(factor_graph_data['jj']).to(self.device)
                        self.factor_graph.idx_ii2jj = torch.from_numpy(factor_graph_data['idx_ii2jj']).to(self.device)
                        self.factor_graph.idx_jj2ii = torch.from_numpy(factor_graph_data['idx_jj2ii']).to(self.device)
                        self.factor_graph.valid_match_j = torch.from_numpy(factor_graph_data['valid_match_j']).to(self.device)
                        self.factor_graph.valid_match_i = torch.from_numpy(factor_graph_data['valid_match_i']).to(self.device)
                        self.factor_graph.Q_ii2jj = torch.from_numpy(factor_graph_data['Q_ii2jj']).to(self.device)
                        self.factor_graph.Q_jj2ii = torch.from_numpy(factor_graph_data['Q_jj2ii']).to(self.device)
                        self.factor_graph.K = factor_graph_data['K']
                        
                        # 更新映射
                        self.factor_graph._update_fid_to_indice_mapping()
                except Exception as e:
                    logger.warning(f"Failed to load factor graph: {e}")
                    logger.error(f"Traceback: {traceback.format_exc()}")

            # rebuild retriever - 需要按顺序重建以保持ID连续性
            # 先清空retriever并重新创建
            self.retriever = load_retriever(self.model, device=self.device)
            
            # 按顺序添加所有keyframes
            for keyframe in self.keyframes:
                # 对于重建，我们不需要查询，直接添加
                feat = self.retriever.prep_features(keyframe.feat)
                feat_np = feat[0].cpu().numpy()
                
                # 使用当前数据库大小作为ID（保证连续性）
                database_size = self.retriever.ivf_builder.ivf.n_images
                id_np = database_size * np.ones(feat_np.shape[0], dtype=np.int64)
                
                # 直接添加到数据库
                self.retriever.add_to_database(feat_np, id_np, None, keyframe.frame_id)
            
            # load status
            status_file = f"{savedir}/status.pkl"
            if os.path.exists(status_file):
                try:
                    with open(status_file, 'rb') as f:
                        status = pickle.load(f)
                        self._mode = status['mode']
                        self.K = status['K']
                        # 验证current_frame_id是否存在
                        if status['current_frame_id'] is not None and status['current_frame_id'] in self.keyframes.keyframes:
                            self._current_frame = self.keyframes[status['current_frame_id']]
                        else:
                            self._current_frame = None
                        self.global_optimizer_tasks = status['global_optimizer_tasks']
                        self.global_index = status['global_index']
                except Exception as e:
                    logger.warning(f"Failed to load status: {e}")
                    logger.error(f"Traceback: {traceback.format_exc()}")
                    
            logger.info(f"Successfully loaded {len(self.keyframes)} keyframes and factor graph")
            
        except Exception as e:
            logger.error(f"Error during load operation: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise

    def save(self, savedir=None):
        """保存状态"""
        if savedir is None:
            savedir = self.workdir
        if savedir is None:
            return
        
        os.system(f"mkdir -p {savedir}")
        logger.info(f"Saving to {savedir}")
        
        # save status
        status_file = f"{savedir}/status.pkl"
        with open(status_file, 'wb') as f:
            pickle.dump({
                'mode': self.mode,
                'K': self.K,
                'current_frame_id': self.current_frame.frame_id if self.current_frame is not None else None,
                'global_optimizer_tasks': self.global_optimizer_tasks,
                'global_index': self.global_index
            }, f)
        
        # save keyframes
        keyframe_dir = f"{savedir}/keyframes"
        os.system(f"mkdir -p {keyframe_dir}")
        for frame in self.keyframes:
            with open(os.path.join(keyframe_dir, f"{frame.frame_id}.pkl"), 'wb') as f:
                pickle.dump(frame, f)
        
        # save factor graph
        factor_graph_file = f"{savedir}/factor_graph.pkl"
        with open(factor_graph_file, 'wb') as f:
            pickle.dump({
                'ii': self.factor_graph.ii.detach().cpu().numpy(),
                'jj': self.factor_graph.jj.detach().cpu().numpy(),
                'idx_ii2jj': self.factor_graph.idx_ii2jj.detach().cpu().numpy(),
                'idx_jj2ii': self.factor_graph.idx_jj2ii.detach().cpu().numpy(),
                'valid_match_j': self.factor_graph.valid_match_j.detach().cpu().numpy(),
                'valid_match_i': self.factor_graph.valid_match_i.detach().cpu().numpy(),
                'Q_ii2jj': self.factor_graph.Q_ii2jj.detach().cpu().numpy(),
                'Q_jj2ii': self.factor_graph.Q_jj2ii.detach().cpu().numpy(),
                'K': self.factor_graph.K
            }, f)

        # save ply
        if len(self.keyframes) > 0:
            C_conf_threshold = 0.5  # 默认置信度阈值
            save_reconstruction(
                savedir,
                'recon.ply',
                self.keyframes,
                C_conf_threshold,
            )
    
    def clear(self):
        """清空所有状态"""
        self.keyframes = SimpleKeyframes(device=self.device)
        if self.K is not None:
            self.keyframes.set_intrinsics(self.K)
        self.factor_graph = FactorGraph(self.model, self.keyframes, self.K, self.device)
        self.tracker = FrameTracker(self.model, self.keyframes, self.device)
        self.retriever = load_retriever(self.model, device=self.device)
        self._mode = Mode.INIT
        self._current_frame = None
        self.global_optimizer_tasks = []
        self.global_index = 0

        # clear workdir
        if self.workdir is not None:
            shutil.rmtree(self.workdir, ignore_errors=True)
            os.makedirs(self.workdir)
            os.makedirs(f"{self.workdir}/checkpoints")
    
    @property
    def mode(self):
        return self._mode
    
    @mode.setter
    def mode(self, value):
        self._mode = value
        logger.info(f"Switching to mode {value}")
    
    @property
    def current_frame(self):
        return self._current_frame
    
    @current_frame.setter
    def current_frame(self, frame):
        self._current_frame = frame

    def read_img(self, img_path):
        img = cv2.imread(img_path)[..., ::-1]
        return img.astype(np.float32) / 255.0

    def add_image(self, img_path):
        # 建图逻辑
        img = self.read_img(img_path)

        if self.current_frame is None:
            if self.start_pose is None:
                T_WC = lietorch.Sim3.Identity(1, device=self.device)
            else:
                T_WC = matrix_to_Sim3(self.start_pose)
        else:
            T_WC = matrix_to_Sim3(self.current_frame.T_WC.matrix()[0])

        # T_WC = (
        #     lietorch.Sim3.Identity(1, device=self.device)
        #     if self.current_frame is None
        #     else self.current_frame.T_WC
        # )

        frame = create_frame(self.global_index, img, T_WC, device=self.device, img_path=img_path)
        self.global_index += 1

        try:
            res = self.new_frame(frame)
            return res
        except Exception as e:
            logger.error(f"Error adding image {img_path}: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                'success': False,
                'added_keyframe': False,
                'mode_changed': False,
                'relocalized': False,
                'error': f"Error adding image {img_path}: {e}"
            }
        
    def append_image(self, img_path):
        # 补图逻辑
        img = self.read_img(img_path)
        T_WC = (
            lietorch.Sim3.Identity(1, device=self.device)
            if self.current_frame is None
            else self.current_frame.T_WC
        )
        frame = create_frame(self.global_index, img, T_WC, device=self.device, img_path=img_path)
        self.global_index += 1
        result = self._handle_reloc_mode(frame, add_to_base=False)
        if result['success']:
            self._add_to_base()
            return result
        
        self.Mode = Mode.TRACKING
        result = self._handle_tracking_mode(frame)
        return result
    
    def new_frame(self, frame):
        """
        处理新帧
        
        Args:
            frame: 新的Frame对象
            
        Returns:
            dict: 处理结果，包含是否成功、是否添加关键帧等信息
        """
        result = {
            'success': False,
            'added_keyframe': False,
            'mode_changed': False,
            'relocalized': False,
            'error': None
        }
        
        try:
            if self.mode == Mode.INIT:
                result = self._handle_init_mode(frame)
            elif self.mode == Mode.TRACKING:
                result = self._handle_tracking_mode(frame)
            elif self.mode == Mode.RELOC:
                result = self._handle_reloc_mode(frame)
            else:
                result['error'] = f"Invalid mode: {self.mode}"
                
        except Exception as e:
            logger.error(f"Error processing frame {frame.frame_id}: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            result['error'] = str(e)
            
        return result
    
    def delete_frame(self, frame_id, remove_from_retiever=True):
        """
        删除指定frame_id的节点及其关联的边
        如果删除导致factor_graph断开成两个图，则只保留前面的图
        
        Args:
            frame_id: 要删除的帧ID
            
        Returns:
            dict: 删除结果
        """
        result = {
            'success': False,
            'removed_frame': False,
            'graph_split': False,
            'kept_components': 0,
            'error': None
        }
        
        try:
            # 检查帧是否存在
            if frame_id not in self.keyframes.frame_ids:
                result['error'] = f"Frame {frame_id} not found in keyframes"
                logger.error(f"NotFoundError deleting frame {frame_id} in {self.keyframes.frame_ids}  {type(frame_id)} vs {type(self.keyframes[0].frame_id)}")
                return result
            
            idx = self.keyframes.frame_ids.index(frame_id)
            
            # 删除相关的因子图边
            self._remove_factor_graph_edges(frame_id)
            
            # 从检索数据库中删除
            if remove_from_retiever:
                self.retriever.remove_from_database(frame_id)
            
            # 从关键帧中删除
            removed = self.keyframes.remove(frame_id)
            result['removed_frame'] = removed
            
            # # 检查图连通性
            # if self._check_graph_connectivity():
            #     result['graph_split'] = True
            #     kept_components = self._cleanup_disconnected_components()
            #     result['kept_components'] = kept_components

            logger.info(f"{idx=}, {len(self.keyframes.frame_ids)}, {self.keyframes.frame_ids}")
            current_idx = max(0, idx - 1)
            current_frame_id = self.keyframes.frame_ids[current_idx] if current_idx < len(self.keyframes.frame_ids) else None
            logger.info(f"{current_idx=}, {current_frame_id=}")
            self.current_frame = self.keyframes[current_frame_id] if current_frame_id is not None else None
            
            result['success'] = True
            logger.info(f"Successfully deleted frame {frame_id}")
            
        except Exception as e:
            logger.error(f"Error deleting frame {frame_id}: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            result['error'] = traceback.format_exc()
            
        return result

    def change_frame(self, frame_id):
        """
        切换当前帧
        
        Args:
            frame_id: 要切换到的帧ID
            
        Returns:
            dict: 切换结果
        """
        result = {
            'success': False,
            'error': None
        }
        
        try:
            # 检查帧是否存在
            if frame_id not in self.keyframes.frame_ids:
                result['error'] = f"Frame {frame_id} not found in keyframes"
                logger.error(f"NotFoundError changing frame {frame_id} in {self.keyframes.frame_ids}  {type(frame_id)} vs {type(self.keyframes[0].frame_id)}")
                return result
            
            # 切换当前帧
            self.current_frame = self.keyframes[frame_id]
            result['success'] = True
            logger.info(f"Successfully changed current frame to {frame_id}")
            
        except Exception as e:
            logger.error(f"Error changing current frame to {frame_id}: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            result['error'] = traceback.format_exc()
            
        return result

    def estimate_initial_pose(self, new_img_path, ref_frame):
        """
        使用MASt3R估计新帧相对于参考帧的初始位姿
        
        Args:
            new_frame: 需要估计位姿的新帧
            ref_frame: 参考帧（通常是最后一个成功的关键帧）
            
        Returns:
            lietorch.Sim3: 估计的世界坐标系下的位姿，失败返回None
        """
        assert ref_frame.img_path is not None
        poses, _, _ = mast3r_estimate(self.model, ref_frame.img_path, new_img_path)
        base_pose = ref_frame.T_WC.matrix()[0].cpu().numpy()
        new_pose = transpose_pose(base_pose, poses[0], poses[1])
        new_pose = torch.from_numpy(new_pose).to(self.device)
        return matrix_to_Sim3(new_pose)

    def show(self, show_cloudpoints=True, show_colors=True, show_poses=True, show_arrows=True, voxel_size=0.1, highlight_nodes=[], use_calib=False, max_points_per_frame=10000, conf_thresh=1.5):
        if not highlight_nodes:
            highlight_nodes = [self.current_frame.frame_id]
        return show(self.keyframes, self.factor_graph, show_cloudpoints, show_colors, show_poses, show_arrows, voxel_size, highlight_nodes, use_calib, max_points_per_frame, conf_thresh)
    
    def _handle_init_mode(self, frame):
        """处理初始化模式"""
        result = {
            'success': True,
            'added_keyframe': True,
            'mode_changed': True,
            'relocalized': False,
            'error': None
        }

        try:
            # 使用单目推理初始化
            X_init, C_init = mast3r_inference_mono(self.model, frame)
            frame.update_pointmap(X_init, C_init)

            # 添加到关键帧
            self._add_keyframe(frame)

            # 切换到跟踪模式
            self.mode = Mode.TRACKING
            self.current_frame = frame

            logger.info(f"Initialized with frame {frame.frame_id}")

            if self.workdir and self.save_checkpoint:
                self.save(f'{self.workdir}/checkpoints/{frame.frame_id}-init')

        except Exception as e:
            result['success'] = False
            result['error'] = traceback.format_exc()
            logger.error(f"Traceback: {traceback.format_exc()}")

        return result

    def _handle_tracking_mode(self, frame, add_to_base=True):
        """处理跟踪模式"""
        result = {
            'success': True,
            'added_keyframe': False,
            'mode_changed': False,
            'relocalized': False,
            'error': None
        }

        try:
            # 使用tracker进行跟踪
            add_new_kf, _, try_reloc = self.tracker.track(frame)

            if try_reloc:
                self.mode = Mode.RELOC
                result['mode_changed'] = True
                logger.info(f"Switching to relocalization mode for frame {frame.frame_id}")

            if add_new_kf:
                # 添加新关键帧
                self._add_keyframe(frame)
                success = self.factor_graph.add_factors([self.current_frame.frame_id], [frame.frame_id], config["local_opt"]["min_match_frac"])
                result['added_keyframe'] = True
                self.current_frame = frame

                if self.workdir and self.save_checkpoint:
                    self.save(f'{self.workdir}/checkpoints/{frame.frame_id}-kf')

                # 处理优化任务
                try:
                    self._process_optimization_tasks(add_to_base)
                    if self.workdir and self.save_checkpoint:
                        self.save(f'{self.workdir}/checkpoints/{frame.frame_id}-kf-opt')
                except Exception as e:
                    logger.error(f"Error in optimization tasks: {e}")
                    logger.error(f"Traceback: {traceback.format_exc()}")
                    # 不影响整体成功状态

        except Exception as e:
            result['success'] = False
            result['error'] = traceback.format_exc()
            logger.error(f"Traceback: {traceback.format_exc()}")

        return result

    def _handle_reloc_mode(self, frame, add_to_base=True):
        """处理重定位模式"""
        result = {
            'success': True,
            'added_keyframe': False,
            'mode_changed': False,
            'relocalized': False,
            'error': None
        }

        try:
            # 进行单目推理
            X, C = mast3r_inference_mono(self.model, frame)
            frame.update_pointmap(X, C)

            # 尝试重定位
            success = self._relocalization(frame, add_to_base)

            if success:
                self.mode = Mode.TRACKING
                result['mode_changed'] = True
                result['relocalized'] = True
                logger.info(f"Successfully relocalized frame {frame.frame_id}")

                if self.workdir and self.save_checkpoint:
                    self.save(f'{self.workdir}/checkpoints/{frame.frame_id}-reloc')
            else:
                logger.warning(f"Relocalization failed for frame {frame.frame_id}")
                # 处理剩余的优化任务
                self._process_optimization_tasks(add_to_base)

        except Exception as e:
            result['success'] = False
            result['error'] = traceback.format_exc()
            logger.error(f"Traceback: {traceback.format_exc()}")

        return result

    def _add_keyframe(self, frame):
        """添加新关键帧"""
        self.keyframes.append(frame)
        self.global_optimizer_tasks.append(frame.frame_id)
        logger.info(f"Added keyframe {frame.frame_id}")

    def _process_optimization_tasks(self, add_to_base=True):
        """处理优化任务"""
        while len(self.global_optimizer_tasks) > 0:
            frame_id = self.global_optimizer_tasks.pop(0)
            try:
                self._process_keyframe_optimization(frame_id, add_to_base)
            except Exception as e:
                logger.error(f"Error processing optimization for frame {frame_id}: {e}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                # 继续处理其他任务

    def _process_keyframe_optimization(self, frame_id, add_to_base=True):
        """处理关键帧优化任务"""
        logger.info(f"Processing keyframe optimization for frame {frame_id}")

        # 检查关键帧是否存在
        if frame_id not in self.keyframes.frame_ids:
            logger.warning(f"Frame {frame_id} not found in keyframes")
            logger.error(f"NotFoundError optimization task: {frame_id} in {self.keyframes.frame_ids}  {type(frame_id)} vs {type(self.keyframes[0].frame_id)}")
            return

        frame = self.keyframes[frame_id]

        # 构建图连接
        retrieval_frame_ids = []

        # 添加连续关键帧（基于frame_ids列表的顺序）
        n_consec = 1
        try:
            current_idx = self.keyframes.frame_ids.index(frame_id)
            for j in range(min(n_consec, current_idx)):
                prev_idx = current_idx - 1 - j
                if prev_idx >= 0:
                    prev_frame_id = self.keyframes.frame_ids[prev_idx]
                    retrieval_frame_ids.append(prev_frame_id)
        except ValueError:
            logger.warning(f"Frame {frame_id} not found in frame_ids list")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return

        # 检索数据库查询
        try:
            if add_to_base:
                retrieval_inds = self.retriever.update(
                    frame,
                    add_after_query=True,
                    k=config["retrieval"]["k"],
                    min_thresh=config["retrieval"]["min_thresh"],
                )
            else:
                self.temp_ids.append(frame_id)

            logger.debug(f"Database retrieval {frame_id}: {retrieval_inds}")
            retrieval_frame_ids += retrieval_inds
        except Exception as e:
            logger.error(f"Error in retrieval database update: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            # 继续处理，即使检索失败

        # 去重并移除当前帧
        retrieval_frame_ids = set(retrieval_frame_ids)
        retrieval_frame_ids.discard(frame_id)
        retrieval_frame_ids = list(retrieval_frame_ids)

        # 使用frame_id进行因子图操作
        if retrieval_frame_ids:
            frame_ids_i = retrieval_frame_ids
            frame_ids_j = [frame_id] * len(retrieval_frame_ids)
            self.factor_graph.add_factors(
                frame_ids_i, frame_ids_j, config["local_opt"]["min_match_frac"]
            )

        # 执行优化
        if config["use_calib"]:
            self.factor_graph.solve_GN_calib()
        else:
            self.factor_graph.solve_GN_rays()

    def _add_to_base(self):
        logger.info(f"Adding to base: {self.temp_ids}")
        for frame_id in self.temp_ids:
            frame = self.keyframes[frame_id]
            self.retriever.update(
                frame,
                add_after_query=True,
                k=config["retrieval"]["k"],
                min_thresh=config["retrieval"]["min_thresh"],
            )
        self.temp_ids = []

    def _clear_temp(self):
        logger.info(f"Clearing new: {self.temp_ids}")
        for frame_id in self.temp_ids:
            self.delete_frame(frame_id, remove_from_retiever=False)
        self.temp_ids = []

    def _relocalization(self, frame, add_to_base=True):
        """重定位逻辑"""
        # 查询检索数据库
        retrieval_inds = self.retriever.update(
            frame,
            add_after_query=False,
            k=config["retrieval"]["k"],
            min_thresh=config["retrieval"]["min_thresh"],
        )

        logger.debug(f"Relocalization retrieval: {retrieval_inds}")

        if not retrieval_inds:
            return False

        # 临时添加到关键帧进行重定位
        self._add_keyframe(frame)
        current_frame_id = frame.frame_id

        try:
            if not retrieval_inds:
                self.keyframes.pop_last()
                return False

            frame_ids_i = [current_frame_id] * len(retrieval_inds)
            frame_ids_j = retrieval_inds
            logger.debug(f"Relocalizing frame {current_frame_id} against frame_ids {retrieval_inds}")

            # 尝试添加因子
            success = self.factor_graph.add_factors(
                frame_ids_i,
                frame_ids_j,
                config["reloc"]["min_match_frac"],
                is_reloc=config["reloc"]["strict"],
            )

            if success:
                # 添加到检索数据库
                if add_to_base:
                    self.retriever.update(
                        frame,
                        add_after_query=True,
                        k=config["retrieval"]["k"],
                        min_thresh=config["retrieval"]["min_thresh"],
                    )
                else:
                    self.temp_ids.append(frame.frame_id)

                # 使用MASt3R估计初始位姿，而不是简单复制
                ref_frame_id = retrieval_inds[0]
                ref_frame = self.keyframes[ref_frame_id]
                
                # 使用estimate_initial_pose方法估计位姿
                estimated_T_WC = self.estimate_initial_pose(frame.img_path, ref_frame)
                
                if estimated_T_WC is not None:
                    self.keyframes[current_frame_id].T_WC = lietorch.Sim3(
                        estimated_T_WC.data
                    )
                    self.keyframes.T_WC[current_frame_id] = estimated_T_WC.data.clone()
                    logger.info(f"Used MASt3R to estimate initial pose for relocalization")
                else:
                    # 如果MASt3R估计失败，回退到使用参考帧的位姿
                    self.keyframes[current_frame_id].T_WC = lietorch.Sim3(
                        ref_frame.T_WC.data
                    )
                    self.keyframes.T_WC[current_frame_id] = ref_frame.T_WC.clone()
                    logger.warning(f"MASt3R pose estimation failed, using reference frame pose")

                # 执行优化
                if config["use_calib"]:
                    self.factor_graph.solve_GN_calib()
                else:
                    self.factor_graph.solve_GN_rays()

                self.current_frame = frame

                return True
            else:
                # 重定位失败，移除临时添加的帧
                self.keyframes.pop_last()
                return False

        except Exception as e:
            logger.error(f"Relocalization error: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            self.keyframes.pop_last()
            return False

    def try_reloc(self, img_path):
        img = self.read_img(img_path)
        T_WC = (
            lietorch.Sim3.Identity(1, device=self.device)
            if self.current_frame is None
            else self.current_frame.T_WC
        )
        frame = create_frame(self.global_index, img, T_WC, device=self.device, img_path=img_path)
        X_init, C_init = mast3r_inference_mono(self.model, frame)
        frame.update_pointmap(X_init, C_init)
        success = self._try_relocalization(frame)
        return success

    def _try_relocalization(self, frame):
        """尝试重定位"""
        if frame.X_canon is None:
            X, C = mast3r_inference_mono(self.model, frame)
            frame.update_pointmap(X, C)
            
        # 查询检索数据库
        retrieval_inds = self.retriever.update(
            frame,
            add_after_query=False,
            k=config["retrieval"]["k"],
            min_thresh=config["retrieval"]["min_thresh"],
        )

        if not retrieval_inds:
            return False
        
        self.keyframes.append(frame)
        current_frame_id = frame.frame_id

        frame_ids_i = [current_frame_id] * len(retrieval_inds)
        frame_ids_j = retrieval_inds

        success  = self.factor_graph.try_add_factors(
            frame_ids_i,
            frame_ids_j,
            config["reloc"]["min_match_frac"],
        )
        self.keyframes.pop_last()

        return success

    def _remove_factor_graph_edges(self, frame_id):
        """从因子图中删除与指定frame_id相关的边"""
        # 使用FactorGraph的新方法来更新映射和删除边
        self.factor_graph.update_mapping_after_frame_removal(frame_id)
        logger.info(f"Removed edges related to frame {frame_id}")

    def _check_graph_connectivity(self):
        """检查因子图是否连通"""
        if len(self.keyframes) <= 1:
            return False

        # 构建邻接表，使用frame_id作为键
        frame_ids = list(self.keyframes.frame_ids)
        adj_list = {fid: [] for fid in frame_ids}

        ii = self.factor_graph.ii.cpu().numpy()
        jj = self.factor_graph.jj.cpu().numpy()

        for i, j in zip(ii, jj):
            if i in adj_list and j in adj_list:
                adj_list[i].append(j)
                adj_list[j].append(i)

        # 使用DFS检查连通性
        visited = set()

        def dfs(node):
            visited.add(node)
            for neighbor in adj_list[node]:
                if neighbor not in visited:
                    dfs(neighbor)

        # 从第一个frame_id开始DFS
        if frame_ids:
            dfs(frame_ids[0])

        # 检查是否所有节点都被访问
        return len(visited) != len(frame_ids)

    def _cleanup_disconnected_components(self):
        """清理断开的图组件，只保留前面的图"""
        if len(self.keyframes) <= 1:
            return 1

        # 构建邻接表，使用frame_id作为键
        frame_ids = list(self.keyframes.frame_ids)
        adj_list = {fid: [] for fid in frame_ids}

        ii = self.factor_graph.ii.cpu().numpy()
        jj = self.factor_graph.jj.cpu().numpy()

        for i, j in zip(ii, jj):
            if i in adj_list and j in adj_list:
                adj_list[i].append(j)
                adj_list[j].append(i)

        # 找到所有连通组件
        visited = set()
        components = []

        def dfs(node, component):
            visited.add(node)
            component.append(node)
            for neighbor in adj_list[node]:
                if neighbor not in visited:
                    dfs(neighbor, component)

        for fid in frame_ids:
            if fid not in visited:
                component = []
                dfs(fid, component)
                components.append(component)

        if len(components) <= 1:
            return len(components)

        # 保留包含第一个frame_id的组件（前面的图）
        first_frame_id = frame_ids[0]
        keep_component = None
        for component in components:
            if first_frame_id in component:
                keep_component = component
                break

        if keep_component is None:
            # 如果没有包含第一个frame_id的组件，保留第一个组件
            keep_component = components[0]

        # 删除其他组件的帧
        frames_to_remove = []
        for component in components:
            if component != keep_component:
                frames_to_remove.extend(component)

        for frame_id in frames_to_remove:
            self.keyframes.remove(frame_id)
            self.retriever.remove_from_database(frame_id)
            logger.info(f"Removed disconnected frame {frame_id}")

        # 重新构建因子图边（移除无效边）
        self.factor_graph._rebuild_factor_graph_edges()

        return 1  # 保留了1个组件


class FactorGraph:
    """
    使用frame_id索引的因子图类
    """

    def __init__(self, model, keyframes: SimpleKeyframes, K=None, device="cuda"):
        self.model = model
        self.frames = keyframes
        self.device = device
        self.cfg = config["local_opt"]

        # 使用frame_id作为键的边存储
        self.ii = torch.as_tensor([], dtype=torch.long, device=self.device)  # frame_id列表
        self.jj = torch.as_tensor([], dtype=torch.long, device=self.device)  # frame_id列表
        self.idx_ii2jj = torch.as_tensor([], dtype=torch.long, device=self.device)
        self.idx_jj2ii = torch.as_tensor([], dtype=torch.long, device=self.device)
        self.valid_match_j = torch.as_tensor([], dtype=torch.bool, device=self.device)
        self.valid_match_i = torch.as_tensor([], dtype=torch.bool, device=self.device)
        self.Q_ii2jj = torch.as_tensor([], dtype=torch.float32, device=self.device)
        self.Q_jj2ii = torch.as_tensor([], dtype=torch.float32, device=self.device)

        # 添加frame_id到索引的映射，用于使用frame_id索引存储
        self.fid_to_indice = {}  # frame_id -> index mapping

        self.window_size = self.cfg["window_size"]
        self.K = K

    def _update_fid_to_indice_mapping(self):
        """更新frame_id到索引的映射"""
        self.fid_to_indice.clear()
        for idx, frame_id in enumerate(self.frames.frame_ids):
            self.fid_to_indice[frame_id] = idx

    def get_index_from_frame_id(self, frame_id):
        """根据frame_id获取在frame_ids列表中的索引"""
        if frame_id not in self.fid_to_indice:
            self._update_fid_to_indice_mapping()
        return self.fid_to_indice.get(frame_id, None)

    def get_indices_from_frame_ids(self, frame_ids):
        """根据frame_id列表获取索引列表"""
        indices = []
        for frame_id in frame_ids:
            idx = self.get_index_from_frame_id(frame_id)
            if idx is not None:
                indices.append(idx)
        return indices

    def remove_frame_from_mapping(self, frame_id):
        """
        从映射中移除指定的frame_id

        Args:
            frame_id: 要移除的frame_id
        """
        if frame_id in self.fid_to_indice:
            del self.fid_to_indice[frame_id]

    def remove_edges_with_frame_id(self, frame_id):
        """
        移除包含指定frame_id的所有边

        Args:
            frame_id: 要移除的frame_id
        """
        # 找到包含该frame_id的边
        mask_ii = self.ii != frame_id
        mask_jj = self.jj != frame_id
        valid_mask = mask_ii & mask_jj

        # 保留不包含该frame_id的边
        self.ii = self.ii[valid_mask]
        self.jj = self.jj[valid_mask]
        self.idx_ii2jj = self.idx_ii2jj[valid_mask]
        self.idx_jj2ii = self.idx_jj2ii[valid_mask]
        self.valid_match_j = self.valid_match_j[valid_mask]
        self.valid_match_i = self.valid_match_i[valid_mask]
        self.Q_ii2jj = self.Q_ii2jj[valid_mask]
        self.Q_jj2ii = self.Q_jj2ii[valid_mask]

    def update_mapping_after_frame_removal(self, removed_frame_id):
        """
        在删除关键帧后更新映射

        Args:
            removed_frame_id: 被删除的frame_id
        """
        # 移除该frame_id的映射
        self.remove_frame_from_mapping(removed_frame_id)

        # 移除包含该frame_id的边
        self.remove_edges_with_frame_id(removed_frame_id)

        # 重新构建frame_id到索引的映射
        self._update_fid_to_indice_mapping()

    def get_frame_by_id(self, frame_id):
        """
        根据frame_id获取关键帧

        Args:
            frame_id: 要获取的frame_id

        Returns:
            Frame对象或None
        """
        return self.frames.keyframes.get(frame_id, None)

    def get_frames_by_ids(self, frame_ids):
        """
        根据frame_id列表获取关键帧列表

        Args:
            frame_ids: frame_id列表

        Returns:
            Frame对象列表
        """
        return [self.frames.keyframes[fid] for fid in frame_ids if fid in self.frames.keyframes]

    def store_data_by_frame_id(self, frame_id, data_type, data):
        """
        使用frame_id索引存储数据的通用方法

        Args:
            frame_id: 要存储数据的frame_id
            data_type: 数据类型 ('pose', 'features', 'matches', etc.)
            data: 要存储的数据
        """
        if not hasattr(self, '_frame_data_storage'):
            self._frame_data_storage = {}

        if frame_id not in self._frame_data_storage:
            self._frame_data_storage[frame_id] = {}

        self._frame_data_storage[frame_id][data_type] = data

    def get_data_by_frame_id(self, frame_id, data_type):
        """
        使用frame_id索引获取存储的数据

        Args:
            frame_id: 要获取数据的frame_id
            data_type: 数据类型

        Returns:
            存储的数据或None
        """
        if not hasattr(self, '_frame_data_storage'):
            return None

        return self._frame_data_storage.get(frame_id, {}).get(data_type, None)

    @property
    def vertices(self):
        return self.frames.frame_ids

    @property
    def edges(self):
        return [(i.item(), j.item()) for i, j in zip(self.ii, self.jj)]

    def __repr__(self):
        return f"FactorGraph(vertices={self.vertices}, edges={self.edges})"

    def add_factors(self, frame_ids_i, frame_ids_j, min_match_frac, is_reloc=False):
        """
        添加因子，使用frame_id而不是索引

        Args:
            frame_ids_i: 第一组frame的ID列表
            frame_ids_j: 第二组frame的ID列表
            min_match_frac: 最小匹配分数
            is_reloc: 是否为重定位
        """
        assert len(frame_ids_i) == len(frame_ids_j)
        edges = [(i, j) for i, j in zip(frame_ids_i, frame_ids_j)]
        logger.debug(f"Adding {len(edges)} factors {edges}")

        # 获取对应的关键帧
        kf_ii = [self.frames[frame_id] for frame_id in frame_ids_i]
        kf_jj = [self.frames[frame_id] for frame_id in frame_ids_j]

        # 检查feat是否存在
        for kf in kf_ii + kf_jj:
            if kf.feat is None:
                kf.feat, kf.pos, _ = self.model._encode_image(kf.img, kf.img_true_shape)

        feat_i = torch.cat([kf_i.feat for kf_i in kf_ii])
        feat_j = torch.cat([kf_j.feat for kf_j in kf_jj])
        pos_i = torch.cat([kf_i.pos for kf_i in kf_ii])
        pos_j = torch.cat([kf_j.pos for kf_j in kf_jj])
        shape_i = [kf_i.img_true_shape for kf_i in kf_ii]
        shape_j = [kf_j.img_true_shape for kf_j in kf_jj]

        (
            idx_i2j,
            idx_j2i,
            valid_match_j,
            valid_match_i,
            Qii,
            Qjj,
            Qji,
            Qij,
        ) = mast3r_match_symmetric(
            self.model, feat_i, pos_i, feat_j, pos_j, shape_i, shape_j
        )

        batch_inds = torch.arange(idx_i2j.shape[0], device=idx_i2j.device)[
            :, None
        ].repeat(1, idx_i2j.shape[1])
        Qj = torch.sqrt(Qii[batch_inds, idx_i2j] * Qji)
        Qi = torch.sqrt(Qjj[batch_inds, idx_j2i] * Qij)

        valid_Qj = Qj > self.cfg["Q_conf"]
        valid_Qi = Qi > self.cfg["Q_conf"]
        valid_j = valid_match_j & valid_Qj
        valid_i = valid_match_i & valid_Qi
        nj = valid_j.shape[1] * valid_j.shape[2]
        ni = valid_i.shape[1] * valid_i.shape[2]
        match_frac_j = valid_j.sum(dim=(1, 2)) / nj
        match_frac_i = valid_i.sum(dim=(1, 2)) / ni

        ii_tensor = torch.as_tensor(frame_ids_i, device=self.device)
        jj_tensor = torch.as_tensor(frame_ids_j, device=self.device)

        # 检查连续边（这里需要根据frame_id的顺序来判断）
        invalid_edges = torch.minimum(match_frac_j, match_frac_i) < min_match_frac

        # 对于frame_id，我们需要检查它们是否在keyframes中是连续的
        consecutive_edges = torch.zeros_like(invalid_edges, dtype=torch.bool)
        for idx, (fid_i, fid_j) in enumerate(zip(frame_ids_i, frame_ids_j)):
            try:
                idx_i = self.frames.frame_ids.index(fid_i)
                idx_j = self.frames.frame_ids.index(fid_j)
                consecutive_edges[idx] = (idx_i == idx_j - 1)
            except ValueError:
                # 如果frame_id不在列表中，不是连续边
                consecutive_edges[idx] = False

        invalid_edges = (~consecutive_edges) & invalid_edges

        # if invalid_edges.any() and is_reloc:
        #     return False

        valid_edges = ~invalid_edges
        
        # check existing edges
        for idx, (fid_i, fid_j) in enumerate(zip(frame_ids_i, frame_ids_j)):
            if (fid_i, fid_j) in self.edges:
                valid_edges[idx] = False

        if len(frame_ids_i) > 1 and torch.sum(valid_edges) <= 1:
            return False
        if len(frame_ids_i) <= 1 and torch.sum(valid_edges) == 0:
            return False

        ii_tensor = ii_tensor[valid_edges]
        jj_tensor = jj_tensor[valid_edges]
        idx_i2j = idx_i2j[valid_edges]
        idx_j2i = idx_j2i[valid_edges]
        valid_match_j = valid_match_j[valid_edges]
        valid_match_i = valid_match_i[valid_edges]
        Qj = Qj[valid_edges]
        Qi = Qi[valid_edges]

        self.ii = torch.cat([self.ii, ii_tensor])
        self.jj = torch.cat([self.jj, jj_tensor])
        self.idx_ii2jj = torch.cat([self.idx_ii2jj, idx_i2j])
        self.idx_jj2ii = torch.cat([self.idx_jj2ii, idx_j2i])
        self.valid_match_j = torch.cat([self.valid_match_j, valid_match_j])
        self.valid_match_i = torch.cat([self.valid_match_i, valid_match_i])
        self.Q_ii2jj = torch.cat([self.Q_ii2jj, Qj])
        self.Q_jj2ii = torch.cat([self.Q_jj2ii, Qi])

        logger.debug(f"{edges} edges: {torch.minimum(match_frac_i, match_frac_j)} over {min_match_frac}")
        logger.info(f"added edges: {[e for e, v in zip(edges, valid_edges) if v]}")

        added_new_edges = valid_edges.sum() > 0
        return added_new_edges
    
    def _compute_matches(self, i, j, conf_threshold=2.5):
        frame_i = self.frames[i]
        frame_j = self.frames[j]
        idx_i2j = self.idx_ii2jj[i]
        valid_match_j = self.valid_match_j[i].squeeze(-1)
        Qii = self.Q_jj2ii[i]
        Qji = self.Q_ii2jj[j]
        # print(idx_i2j.shape)
        # print(valid_match_j.shape)
        # print(Qii.shape)
        # print(Qji.shape)
        valid_conf = (Qii.squeeze(-1) > conf_threshold) & (Qji[idx_i2j].squeeze(-1) > conf_threshold)
        valid_matches = valid_match_j & valid_conf
        confs = torch.min(Qii.squeeze(-1), Qji[idx_i2j].squeeze(-1))[valid_matches]
        confs /= confs.max()

        valid_idx_i2j = idx_i2j[valid_matches]
        h_i, w_i = frame_i.get_img_size()
        h_j, w_j = frame_j.get_img_size()

        valid_indices_i = torch.arange(h_i * w_i, device=self.device)[valid_matches]
        pixels_i = matching.lin_to_pixel(valid_idx_i2j, w_j).float()
        pixels_j = matching.lin_to_pixel(valid_indices_i, w_i).float()

        # Apply RANSAC filtering if we have enough points
        if pixels_i.shape[0] >= 4:
            # Convert to numpy for OpenCV
            pts1 = pixels_i.cpu().numpy()
            pts2 = pixels_j.cpu().numpy()
            
            # Use OpenCV to perform RANSAC
            try:
                _, mask = cv2.findHomography(pts1, pts2, cv2.RANSAC, 3.0)
                mask = mask.flatten().astype(bool)
                
                # Filter points using RANSAC result
                pixels_i = pixels_i[mask]
                pixels_j = pixels_j[mask]
                confs = confs[mask]
            except cv2.error:
                # If RANSAC fails, continue with original points
                pass

        return pixels_i, pixels_j, confs
    
    def try_add_factors(self, frame_ids_i, frame_ids_j, min_match_frac):
        valid_edges = self.try_valid_edges(frame_ids_i, frame_ids_j, min_match_frac)

        # print(f"{consecutive_edges=}")
        # print(f"{invalid_edges=}")
        # print(f"{valid_edges=}")
        # print(f"{torch.sum(valid_edges)=}")

        if len(frame_ids_i) > 1 and torch.sum(valid_edges) <= 1:
            return False
        if len(frame_ids_i) <= 1 and torch.sum(valid_edges) == 0:
            return False
        return True

    def try_valid_edges(self, frame_ids_i, frame_ids_j, min_match_frac):
        edges = [(i, j) for i, j in zip(frame_ids_i, frame_ids_j)]
        print(f"{edges=}")
        kf_ii = [self.frames[frame_id] for frame_id in frame_ids_i]
        kf_jj = [self.frames[frame_id] for frame_id in frame_ids_j]

        for kf in kf_ii + kf_jj:
            if kf.feat is None:
                kf.feat, kf.pos, _ = self.model._encode_image(kf.img, kf.img_true_shape)

        feat_i = torch.cat([kf_i.feat for kf_i in kf_ii])
        feat_j = torch.cat([kf_j.feat for kf_j in kf_jj])
        pos_i = torch.cat([kf_i.pos for kf_i in kf_ii])
        pos_j = torch.cat([kf_j.pos for kf_j in kf_jj])
        shape_i = [kf_i.img_true_shape for kf_i in kf_ii]
        shape_j = [kf_j.img_true_shape for kf_j in kf_jj]

        (
            idx_i2j,
            idx_j2i,
            valid_match_j,
            valid_match_i,
            Qii,
            Qjj,
            Qji,
            Qij,
        ) = mast3r_match_symmetric(
            self.model, feat_i, pos_i, feat_j, pos_j, shape_i, shape_j
        )

        batch_inds = torch.arange(idx_i2j.shape[0], device=idx_i2j.device)[
            :, None
        ].repeat(1, idx_i2j.shape[1])
        Qj = torch.sqrt(Qii[batch_inds, idx_i2j] * Qji)
        Qi = torch.sqrt(Qjj[batch_inds, idx_j2i] * Qij)

        valid_Qj = Qj > self.cfg["Q_conf"]
        valid_Qi = Qi > self.cfg["Q_conf"]
        valid_j = valid_match_j & valid_Qj
        valid_i = valid_match_i & valid_Qi
        nj = valid_j.shape[1] * valid_j.shape[2]
        ni = valid_i.shape[1] * valid_i.shape[2]
        match_frac_j = valid_j.sum(dim=(1, 2)) / nj
        match_frac_i = valid_i.sum(dim=(1, 2)) / ni

        ii_tensor = torch.as_tensor(frame_ids_i, device=self.device)
        jj_tensor = torch.as_tensor(frame_ids_j, device=self.device)

        invalid_edges = torch.minimum(match_frac_j, match_frac_i) < min_match_frac
        # print(f"{match_frac_i=}")
        # print(f"{match_frac_j=}")
        print(f"{torch.minimum(match_frac_j, match_frac_i)=}")
        # print(f"{min_match_frac=}")
        # print(f"{invalid_edges=}")
        consecutive_edges = ii_tensor == (jj_tensor - 1)
        invalid_edges = (~consecutive_edges) & invalid_edges
        valid_edges = ~invalid_edges
        return valid_edges

    def get_unique_frame_ids(self):
        """获取所有唯一的frame_id"""
        return torch.unique(torch.cat([self.ii, self.jj]), sorted=True)

    def solve_GN_rays(self, auto_estimate_intrinsics=True):
        """
        使用射线约束求解
        
        Args:
            auto_estimate_intrinsics: 是否在优化后自动估计内参
        """
        try:
            # 转换frame_id到索引进行求解
            frame_ids = self.get_unique_frame_ids()
            if len(frame_ids) == 0:
                return None

            frame_id_to_idx = {fid.item(): i for i, fid in enumerate(frame_ids)}

            # 转换边的frame_id到索引
            ii_idx = torch.tensor([frame_id_to_idx[fid.item()] for fid in self.ii], device=self.device)
            jj_idx = torch.tensor([frame_id_to_idx[fid.item()] for fid in self.jj], device=self.device)

            # 创建临时的keyframes列表（按索引顺序）
            temp_keyframes = [self.frames[fid.item()] for fid in frame_ids]

            # 准备位姿和点云数据
            T_WCs_data = torch.stack([kf.T_WC.data for kf in temp_keyframes])
            T_WCs = lietorch.Sim3(T_WCs_data)
            Xs = torch.stack([kf.X_canon for kf in temp_keyframes])
            Cs = torch.stack([kf.C for kf in temp_keyframes])

            # 获取配置参数
            C_thresh = self.cfg["C_conf"]
            Q_thresh = self.cfg["Q_conf"]
            max_iter = self.cfg["max_iters"]
            sigma_ray = self.cfg["sigma_ray"]
            sigma_dist = self.cfg["sigma_dist"]
            delta_thresh = self.cfg["delta_norm"]

            # 调用原始求解器
            pose_data = T_WCs.data[:, 0, :]
            mast3r_slam_backends.gauss_newton_rays(
                pose_data,
                Xs,
                Cs,
                ii_idx,
                jj_idx,
                self.idx_ii2jj,
                self.valid_match_j,
                self.Q_ii2jj,
                sigma_ray,
                sigma_dist,
                C_thresh,
                Q_thresh,
                max_iter,
                delta_thresh,
            )

            # 更新keyframes的位姿
            updated_T_WCs = lietorch.Sim3(pose_data)
            for i, fid in enumerate(frame_ids):
                frame_id = fid.item()
                self.frames[frame_id].T_WC = lietorch.Sim3(pose_data[i][None])
                self.frames.T_WC[frame_id] = pose_data[i][None].clone()

            # 在优化后自动估计内参（如果没有提供内参且有足够的关键帧）
            if auto_estimate_intrinsics and self.K is None and len(self.frames) >= 3:
                self._estimate_intrinsics_after_optimization()

            return updated_T_WCs

        except Exception as e:
            logger.error(f"Error in solve_GN_rays: {traceback.format_exc()}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def solve_GN_calib(self):
        """使用标定约束求解"""
        try:
            # 使用原始FactorGraph的求解逻辑，但适配frame_id

            # 转换frame_id到索引进行求解
            frame_ids = self.get_unique_frame_ids()
            if len(frame_ids) == 0:
                return None

            frame_id_to_idx = {fid.item(): i for i, fid in enumerate(frame_ids)}

            # 转换边的frame_id到索引
            ii_idx = torch.tensor([frame_id_to_idx[fid.item()] for fid in self.ii], device=self.device)
            jj_idx = torch.tensor([frame_id_to_idx[fid.item()] for fid in self.jj], device=self.device)

            # 创建临时的keyframes列表（按索引顺序）
            temp_keyframes = [self.frames[fid.item()] for fid in frame_ids]

            # 准备位姿和点云数据
            T_WCs_data = torch.stack([kf.T_WC.data for kf in temp_keyframes])
            T_WCs = lietorch.Sim3(T_WCs_data)
            Xs = torch.stack([kf.X_canon for kf in temp_keyframes])
            Cs = torch.stack([kf.C for kf in temp_keyframes])

            # 获取配置参数
            C_thresh = self.cfg["C_conf"]
            Q_thresh = self.cfg["Q_conf"]
            max_iter = self.cfg["max_iters"]
            sigma_ray = self.cfg["sigma_ray"]
            sigma_dist = self.cfg["sigma_dist"]
            delta_thresh = self.cfg["delta_norm"]

            # 调用原始求解器
            pose_data = T_WCs.data[:, 0, :]
            mast3r_slam_backends.gauss_newton_calib(
                pose_data,
                Xs,
                Cs,
                ii_idx,
                jj_idx,
                self.idx_ii2jj,
                self.valid_match_j,
                self.Q_ii2jj,
                self.K,
                sigma_ray,
                sigma_dist,
                C_thresh,
                Q_thresh,
                max_iter,
                delta_thresh,
            )

            # 更新keyframes的位姿
            updated_T_WCs = lietorch.Sim3(pose_data)
            for i, fid in enumerate(frame_ids):
                frame_id = fid.item()
                self.frames[frame_id].T_WC = lietorch.Sim3(pose_data[i][None])
                self.frames.T_WC[frame_id] = pose_data[i][None].clone()

            return updated_T_WCs

        except Exception as e:
            logger.error(f"Error in solve_GN_calib: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def _rebuild_factor_graph_edges(self):
        """重建因子图边，移除无效的边"""
        valid_frame_ids = set(self.frames.frame_ids)

        ii = self.ii.cpu().numpy()
        jj = self.jj.cpu().numpy()

        # 创建掩码，保留有效的边（两个frame_id都存在）
        mask = np.array([i in valid_frame_ids and j in valid_frame_ids for i, j in zip(ii, jj)])

        # 更新所有相关的张量
        self.ii = self.ii[torch.from_numpy(mask)]
        self.jj = self.jj[torch.from_numpy(mask)]
        self.idx_ii2jj = self.idx_ii2jj[torch.from_numpy(mask)]
        self.idx_jj2ii = self.idx_jj2ii[torch.from_numpy(mask)]
        self.valid_match_j = self.valid_match_j[torch.from_numpy(mask)]
        self.valid_match_i = self.valid_match_i[torch.from_numpy(mask)]
        self.Q_ii2jj = self.Q_ii2jj[torch.from_numpy(mask)]
        self.Q_jj2ii = self.Q_jj2ii[torch.from_numpy(mask)]

        logger.info(f"Rebuilt factor graph with {len(self.ii)} edges for {len(valid_frame_ids)} frames")

    def global_optimize(self,
                       lr1=0.2, niter1=500,
                       lr2=0.02, niter2=500,
                       opt_pose=True, opt_intrinsics=True, 
                       opt_depth=False, opt_depth_scale=True,
                       schedule='cosine',
                       loss_3d_weight=1.0, loss_2d_weight=1.0,
                       verbose=True):
        """
        仿照sparse_scene_optimizer的全局优化函数
        对FactorGraph进行两阶段全局优化

        Args:
            lr1: 第一阶段学习率（3D匹配优化）
            niter1: 第一阶段迭代次数
            lr2: 第二阶段学习率（2D重投影优化）
            niter2: 第二阶段迭代次数
            opt_intrinsics: 是否优化内参
            opt_depth: 是否优化深度
            schedule: 学习率调度策略 ('cosine', 'linear', 'constant')
            loss_3d_weight: 3D损失权重
            loss_2d_weight: 2D损失权重
            verbose: 是否输出详细信息

        Returns:
            dict: 优化结果，包含优化后的位姿、内参等
        """
        try:
            import torch.nn as nn
            import torch.nn.functional as F
            from tqdm import tqdm

            if verbose:
                logger.info("Starting global optimization...")
                logger.info(f"Stage 1: lr={lr1}, niter={niter1} (3D matching)")
                logger.info(f"Stage 2: lr={lr2}, niter={niter2} (2D reprojection)")

            # 获取参与优化的frame_ids
            frame_ids = self.get_unique_frame_ids()
            if len(frame_ids) < 2:
                logger.warning("Not enough frames for global optimization")
                return None

            frame_ids_list = [fid.item() for fid in frame_ids]
            n_frames = len(frame_ids_list)

            if verbose:
                logger.info(f"Optimizing {n_frames} frames: {frame_ids_list}")

            # 初始化优化参数
            device = self.device
            dtype = torch.float32

            # 外参参数 - 使用四元数+平移表示
            vec0001 = torch.tensor((0, 0, 0, 1), dtype=dtype, device=device)
            quats = [nn.Parameter(vec0001.clone()) for _ in range(n_frames)]
            trans = [nn.Parameter(torch.zeros(3, device=device, dtype=dtype)) for _ in range(n_frames)]

            # 内参参数
            if self.K is not None:
                # 使用现有内参初始化
                focal_init = (self.K[0, 0] + self.K[1, 1]) / 2
                pp_init = self.K[:2, 2]
                log_focal = nn.Parameter(focal_init.log().to(dtype))
                pp = nn.Parameter(pp_init.to(dtype))
            else:
                # 估计初始内参
                h, w = self._get_image_size()
                focal_init = max(h, w) * 0.8  # 初始焦距估计
                pp_init = torch.tensor([w/2, h/2], device=device, dtype=dtype)
                log_focal = nn.Parameter(torch.log(torch.tensor(focal_init, dtype=dtype, device=device)))
                pp = nn.Parameter(pp_init)

            # 深度参数 - 使用对数深度
            log_depths = []
            for fid in frame_ids_list:
                frame = self.frames[fid]
                if frame.X_canon is not None:
                    depth = frame.X_canon[..., 2].clamp(min=1e-4)
                    log_depths.append(nn.Parameter(depth.log().flatten().to(dtype)))
                else:
                    # 如果没有深度，使用默认值
                    h, w = frame.img.shape[:2]
                    default_depth = torch.ones(h * w, device=device, dtype=dtype)
                    log_depths.append(nn.Parameter(default_depth.log()))
            depth_scales = [nn.Parameter(torch.ones(1, device=device, dtype=dtype)) for _ in range(n_frames)]

            # 初始化位姿参数
            self._initialize_pose_parameters(frame_ids_list, quats, trans)

            # 定义学习率调度器
            def get_schedule_fn(schedule_type):
                if schedule_type == 'cosine':
                    return lambda alpha, lr_base, lr_end: lr_end + (lr_base - lr_end) * (1 + np.cos(np.pi * alpha)) / 2
                elif schedule_type == 'linear':
                    return lambda alpha, lr_base, lr_end: lr_base * (1 - alpha) + lr_end * alpha
                else:  # constant
                    return lambda alpha, lr_base, lr_end: lr_base

            schedule_fn = get_schedule_fn(schedule)

            # 第一阶段：3D匹配优化
            if niter1 > 0:
                result_stage1 = self._optimize_stage1(
                    frame_ids_list, quats, trans, log_focal, pp, log_depths, depth_scales,
                    lr1, niter1, schedule_fn, loss_3d_weight, opt_pose, opt_intrinsics, opt_depth, opt_depth_scale, verbose
                )
            else:
                result_stage1 = None

            # 第二阶段：2D重投影优化
            result_stage2 = None
            if niter2 > 0:
                result_stage2 = self._optimize_stage2(
                    frame_ids_list, quats, trans, log_focal, pp, log_depths,
                    lr2, niter2, schedule_fn, loss_2d_weight, opt_intrinsics, opt_depth, verbose
                )

            # 应用优化结果
            final_result = result_stage2 if result_stage2 is not None else result_stage1
            if final_result is not None:
                self._apply_optimization_results(frame_ids_list, final_result)

                if verbose:
                    logger.info("Global optimization completed successfully")

                return final_result
            else:
                logger.warning("Global optimization failed")
                return None

        except Exception as e:
            logger.error(f"Error in global optimization: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def _get_image_size(self):
        """获取图像尺寸"""
        if self.frames.h is not None and self.frames.w is not None:
            return self.frames.h, self.frames.w
        assert len(self.frames) > 0
        return self.frames[0].uimg.shape[:2]

    def _initialize_pose_parameters(self, frame_ids_list, quats, trans):
        """初始化位姿参数"""
        import roma

        for i, fid in enumerate(frame_ids_list):
            frame = self.frames[fid]
            T_WC = frame.T_WC.matrix()[0]  # [4, 4]

            # 提取旋转和平移
            R = T_WC[:3, :3]
            t = T_WC[:3, 3]

            # 转换为四元数
            quat = roma.rotmat_to_unitquat(R)
            quats[i].data[:] = quat
            trans[i].data[:] = t

    def _optimize_stage1(self, frame_ids_list, quats, trans, log_focal, pp, log_depths, depth_scales,
                        lr, niter, schedule_fn, loss_weight, opt_pose, opt_intrinsics, opt_depth, opt_depth_scale, verbose):
        """第一阶段：3D匹配优化"""
        import torch.optim as optim

        # 设置参数的可训练性
        for i in range(len(frame_ids_list)):
            quats[i].requires_grad_(opt_pose)
            trans[i].requires_grad_(opt_pose)
        log_focal.requires_grad_(opt_intrinsics)
        pp.requires_grad_(opt_intrinsics)
        for log_depth in log_depths:
            log_depth.requires_grad_(opt_depth)
        for depth_scale in depth_scales:
            depth_scale.requires_grad_(opt_depth_scale)

        # 创建优化器
        params = []
        if opt_pose:
            params += quats + trans
        if opt_intrinsics:
            params += [log_focal, pp]
        if opt_depth:
            params += log_depths
        if opt_depth_scale:
            params += depth_scales

        optimizer = optim.Adam(params, lr=1.0, weight_decay=0, betas=(0.9, 0.9))

        if verbose:
            logger.info(f"Stage 1: Optimizing {len(params)} parameters")

        # 优化循环
        for iter_idx in range(niter):
            alpha = iter_idx / max(niter - 1, 1)
            current_lr = schedule_fn(alpha, lr, 0.0)

            # 调整学习率
            for param_group in optimizer.param_groups:
                param_group['lr'] = current_lr

            optimizer.zero_grad()

            # 计算3D匹配损失
            # _log_depths = [log_depth * depth_scale for log_depth, depth_scale in zip(log_depths, depth_scales)]
            loss = self._compute_3d_matching_loss(
                frame_ids_list, quats, trans, log_focal, pp, log_depths, depth_scales
            ) * loss_weight

            if torch.isnan(loss) or torch.isinf(loss):
                logger.warning(f"Invalid loss at iteration {iter_idx}: {loss}")
                break

            loss.backward()
            optimizer.step()

            # 归一化四元数
            import torch.nn.functional as F
            for quat in quats:
                quat.data[:] = F.normalize(quat.data, dim=0)

            if verbose and (iter_idx % 10 == 0 or iter_idx == niter - 1):
                logger.info(f"Stage 1 iter {iter_idx}/{niter}: loss={loss.item():.6f}, lr={current_lr:.6f}")

        # 构建结果
        return self._build_optimization_result(frame_ids_list, quats, trans, log_focal, pp, log_depths)

    def _optimize_stage2(self, frame_ids_list, quats, trans, log_focal, pp, log_depths,
                        lr, niter, schedule_fn, loss_weight, opt_intrinsics, opt_depth, verbose):
        """第二阶段：2D重投影优化"""
        import torch.optim as optim

        # 设置参数的可训练性（第二阶段通常优化更多参数）
        for i in range(len(frame_ids_list)):
            quats[i].requires_grad_(True)
            trans[i].requires_grad_(True)
        log_focal.requires_grad_(opt_intrinsics)
        pp.requires_grad_(opt_intrinsics)
        for log_depth in log_depths:
            log_depth.requires_grad_(opt_depth)

        # 创建优化器
        params = quats + trans
        if opt_intrinsics:
            params += [log_focal, pp]
        if opt_depth:
            params += log_depths

        optimizer = optim.Adam(params, lr=1.0, weight_decay=0, betas=(0.9, 0.9))

        if verbose:
            logger.info(f"Stage 2: Optimizing {len(params)} parameters")

        # 优化循环
        for iter_idx in range(niter):
            alpha = iter_idx / max(niter - 1, 1)
            current_lr = schedule_fn(alpha, lr, 0.0)

            # 调整学习率
            for param_group in optimizer.param_groups:
                param_group['lr'] = current_lr

            optimizer.zero_grad()

            # 计算2D重投影损失
            loss = self._compute_2d_reprojection_loss(
                frame_ids_list, quats, trans, log_focal, pp, log_depths
            ) * loss_weight

            if torch.isnan(loss) or torch.isinf(loss):
                logger.warning(f"Invalid loss at iteration {iter_idx}: {loss}")
                break

            loss.backward()
            optimizer.step()

            # 归一化四元数
            import torch.nn.functional as F
            for quat in quats:
                quat.data[:] = F.normalize(quat.data, dim=0)

            if verbose and (iter_idx % 50 == 0 or iter_idx == niter - 1):
                logger.info(f"Stage 2 iter {iter_idx}/{niter}: loss={loss.item():.6f}, lr={current_lr:.6f}")

        # 构建结果
        return self._build_optimization_result(frame_ids_list, quats, trans, log_focal, pp, log_depths)

    def _compute_3d_matching_loss(self, frame_ids_list, quats, trans, log_focal, pp, log_depths, depth_scales):
        """计算3D匹配损失"""
        import torch.nn.functional as F
        import roma

        total_loss = 0.0
        total_weight = 0.0

        # 构建相机位姿和内参
        poses = self._build_poses_from_params(quats, trans)
        K = self._build_intrinsics_from_params(log_focal, pp)

        # 遍历所有边
        for edge_idx in range(len(self.ii)):
            fid_i = self.ii[edge_idx].item()
            fid_j = self.jj[edge_idx].item()

            # 检查frame_id是否在当前优化的帧中
            if fid_i not in frame_ids_list or fid_j not in frame_ids_list:
                continue

            idx_i = frame_ids_list.index(fid_i)
            idx_j = frame_ids_list.index(fid_j)

            # 获取匹配点
            valid_mask = self.valid_match_j[edge_idx]
            if not valid_mask.any():
                continue

            # 获取3D点
            frame_i = self.frames[fid_i]
            frame_j = self.frames[fid_j]

            if frame_i.X_canon is None or frame_j.X_canon is None:
                continue

            # 使用匹配索引获取对应的3D点
            idx_ii2jj = self.idx_ii2jj[edge_idx]

            # 获取有效的匹配点 - 使用原始图像坐标
            h_i, w_i = frame_i.img.shape[-2:]
            h_j, w_j = frame_j.img.shape[-2:]

            # 获取像素坐标
            y_coords_i, x_coords_i = torch.meshgrid(torch.arange(h_i), torch.arange(w_i), indexing='ij')
            pixel_coords_i = torch.stack([x_coords_i.flatten(), y_coords_i.flatten()], dim=1).float().to(self.device)

            y_coords_j, x_coords_j = torch.meshgrid(torch.arange(h_j), torch.arange(w_j), indexing='ij')
            pixel_coords_j = torch.stack([x_coords_j.flatten(), y_coords_j.flatten()], dim=1).float().to(self.device)

            # 获取有效匹配的像素坐标
            valid_pixels_i = pixel_coords_i[valid_mask.view(-1)]
            valid_pixels_j = pixel_coords_j[idx_ii2jj.view(-1)][valid_mask.view(-1)]

            if len(valid_pixels_i) == 0:
                continue

            # 如果优化深度，则重新计算3D点
            if len(log_depths) > 0 and log_depths[idx_i] is not None and log_depths[idx_j] is not None:
                # 从深度图重新计算3D点
                depth_i = (torch.exp(log_depths[idx_i]) * depth_scales[idx_i]).view(h_i, w_i)
                depth_j = (torch.exp(log_depths[idx_j]) * depth_scales[idx_j]).view(h_j, w_j)

                # 反投影到3D
                # print(valid_pixels_i.shape)
                # print(depth_i.shape)
                pts3d_i = self._unproject_points(valid_pixels_i, depth_i, K)
                pts3d_j = self._unproject_points(valid_pixels_j, depth_j, K)
            else:
                # 使用原始X_canon
                pts3d_i = frame_i.X_canon.view(-1, 3)[valid_mask.view(-1)]
                pts3d_j_matched = frame_j.X_canon.view(-1, 3)[idx_ii2jj.view(-1)][valid_mask.view(-1)]
                pts3d_j = pts3d_j_matched

            # 变换到世界坐标系
            pts3d_i_world = self._transform_points(pts3d_i, poses[idx_i])
            pts3d_j_world = self._transform_points(pts3d_j, poses[idx_j])

            # 计算3D距离损失 - 使用更robust的损失函数
            dist = torch.norm(pts3d_i_world - pts3d_j_world, dim=1)

            # 使用Huber损失来处理outliers
            huber_delta = 0.1  # 可调参数
            huber_loss = torch.where(dist < huber_delta,
                                   0.5 * dist ** 2,
                                   huber_delta * (dist - 0.5 * huber_delta))

            # 使用置信度加权
            confidence = self.Q_ii2jj[edge_idx][valid_mask.view(-1)].squeeze(-1)
            loss = (confidence * huber_loss).sum() / confidence.sum()
            total_loss += loss

        # 添加点云形状保持正则化项
        shape_preservation_loss = self._compute_shape_preservation_loss(frame_ids_list, log_focal, pp, log_depths, poses)
        total_loss += shape_preservation_loss

        # 添加深度图平滑性正则化项
        if len(log_depths) > 0:
            depth_smoothness_loss = self._compute_depth_smoothness_loss(frame_ids_list, log_depths, depth_scales)
            total_loss += depth_smoothness_loss

        return total_loss

    def _compute_depth_smoothness_loss(self, frame_ids_list, log_depths, depth_scales):
        """
        计算深度图平滑性正则化损失
        通过鼓励相邻像素深度值相似来保持深度图的平滑性
        """
        smoothness_loss = 0.0
        total_pixels = 0

        # 对每个帧计算深度图平滑性损失
        for idx, fid in enumerate(frame_ids_list):
            if log_depths[idx] is None:
                continue
                
            frame = self.frames[fid]
            h, w = frame.img.shape[-2:]
            
            # 获取当前优化的深度图
            depth = (torch.exp(log_depths[idx]) * depth_scales[idx]).view(h, w)
            
            # 计算水平和垂直方向的梯度
            depth_dx = depth[:, 1:] - depth[:, :-1]  # 水平方向差分
            depth_dy = depth[1:, :] - depth[:-1, :]  # 垂直方向差分
            
            # 计算梯度的L1范数（鼓励平滑）
            smoothness_loss += torch.sum(torch.abs(depth_dx)) + torch.sum(torch.abs(depth_dy))
            total_pixels += depth_dx.numel() + depth_dy.numel()
            
        # 返回平均平滑性损失（加权）
        if total_pixels > 0:
            return smoothness_loss / total_pixels * 0.1  # 0.1是权重，可根据需要调整
        else:
            return torch.tensor(0.0, device=self.device)

    def _compute_3d_matching_loss2(self, frame_ids_list, quats, trans, log_focal, pp, log_depths, depth_scales):
        total_loss = 0.0
        total_weight = 0.0

        # 构建相机位姿和内参
        poses = self._build_poses_from_params(quats, trans)
        K = self._build_intrinsics_from_params(log_focal, pp)

        # 遍历所有边
        for edge_idx in range(len(self.ii)):
            fid_i = self.ii[edge_idx].item()
            fid_j = self.jj[edge_idx].item()

            # 检查frame_id是否在当前优化的帧中
            if fid_i not in frame_ids_list or fid_j not in frame_ids_list:
                continue

            idx_i = frame_ids_list.index(fid_i)
            idx_j = frame_ids_list.index(fid_j)

            valid_pixels_i, valid_pixels_j, confidence = self._compute_matches(idx_i, idx_j)

            if len(valid_pixels_i) == 0:
                continue

            # 从深度图重新计算3D点
            depth_i = torch.exp(log_depths[idx_i]) * depth_scales[idx_i]
            depth_j = torch.exp(log_depths[idx_j]) * depth_scales[idx_j]

            # 反投影到3D
            pts3d_i = self._unproject_points(valid_pixels_i, depth_i, K)
            pts3d_j = self._unproject_points(valid_pixels_j, depth_j, K)

            # 变换到世界坐标系
            pts3d_i_world = self._transform_points(pts3d_i, poses[idx_i])
            pts3d_j_world = self._transform_points(pts3d_j, poses[idx_j])

            # 计算3D距离损失 - 使用更robust的损失函数
            dist = torch.norm(pts3d_i_world - pts3d_j_world, dim=1)

            # 使用Huber损失来处理outliers
            huber_delta = 0.1  # 可调参数
            huber_loss = torch.where(dist < huber_delta,
                                   0.5 * dist ** 2,
                                   huber_delta * (dist - 0.5 * huber_delta))

            # 使用置信度加权
            loss = (confidence * huber_loss).sum()
            weight = confidence.sum()

            total_loss += loss
            total_weight += weight

        return total_loss / (total_weight + 1e-8)

    def _compute_shape_preservation_loss(self, frame_ids_list, log_focal, pp, log_depths, poses):
        """
        计算点云形状保持正则化损失
        通过保持点云中相邻点之间的距离来防止点云变形
        """
        import torch.nn.functional as F
        import roma

        shape_loss = 0.0
        total_pairs = 0

        # 对每个帧计算形状保持损失
        for idx, fid in enumerate(frame_ids_list):
            frame = self.frames[fid]
            if frame.X_canon is None:
                continue

            h, w = frame.img.shape[-2:]
            
            # 如果优化深度，则使用优化后的深度
            if len(log_depths) > 0 and log_depths[idx] is not None:
                depth = torch.exp(log_depths[idx]).view(h, w)
                # 重新计算3D点
                y_coords, x_coords = torch.meshgrid(torch.arange(h), torch.arange(w), indexing='ij')
                pixel_coords = torch.stack([x_coords.flatten(), y_coords.flatten()], dim=1).float().to(self.device)
                K = self._build_intrinsics_from_params(log_focal, pp)
                pts3d = self._unproject_points(pixel_coords, depth, K)
            else:
                # 使用原始点云
                pts3d = frame.X_canon.view(-1, 3)

            # 采样点对以计算距离保持损失
            # 为避免计算复杂度过高，只计算相邻点对
            n_points = pts3d.shape[0]
            if n_points < 2:
                continue

            # 创建邻近点对索引（这里简化为连续点对）
            n_samples = min(1000, n_points - 1)  # 限制样本数量
            indices = torch.randperm(n_points - 1)[:n_samples] if n_points > n_samples + 1 else torch.arange(n_points - 1)
            
            if len(indices) == 0:
                continue

            # 计算原始点对距离（使用原始点云）
            original_pts = frame.X_canon.view(-1, 3)
            p1_orig = original_pts[indices]
            p2_orig = original_pts[indices + 1]
            original_distances = torch.norm(p1_orig - p2_orig, dim=1)

            # 计算当前点对距离（使用当前优化的点云）
            p1_current = pts3d[indices]
            p2_current = pts3d[indices + 1]
            current_distances = torch.norm(p1_current - p2_current, dim=1)

            # 计算距离变化损失（Huber损失）
            distance_diff = torch.abs(current_distances - original_distances)
            huber_delta = 0.01
            huber_loss = torch.where(distance_diff < huber_delta,
                                   0.5 * distance_diff ** 2,
                                   huber_delta * (distance_diff - 0.5 * huber_delta))

            shape_loss += huber_loss.sum()
            total_pairs += len(indices)

        # 返回平均形状保持损失（加权）
        if total_pairs > 0:
            return shape_loss / total_pairs * 0.1  # 0.1是权重，可根据需要调整
        else:
            return torch.tensor(0.0, device=self.device)

    def _compute_2d_reprojection_loss(self, frame_ids_list, quats, trans, log_focal, pp, log_depths):
        """计算2D重投影损失"""
        import torch.nn.functional as F
        import roma

        total_loss = 0.0
        total_weight = 0.0

        # 构建相机位姿和内参
        poses = self._build_poses_from_params(quats, trans)
        K = self._build_intrinsics_from_params(log_focal, pp)

        # 遍历所有边
        for edge_idx in range(len(self.ii)):
            fid_i = self.ii[edge_idx].item()
            fid_j = self.jj[edge_idx].item()

            # 检查frame_id是否在当前优化的帧中
            if fid_i not in frame_ids_list or fid_j not in frame_ids_list:
                continue

            idx_i = frame_ids_list.index(fid_i)
            idx_j = frame_ids_list.index(fid_j)

            # 获取匹配点
            valid_mask = self.valid_match_j[edge_idx]
            if not valid_mask.any():
                continue

            # 获取帧数据
            frame_i = self.frames[fid_i]
            frame_j = self.frames[fid_j]

            if frame_i.X_canon is None:
                continue

            # 获取3D点和2D像素坐标
            idx_ii2jj = self.idx_ii2jj[edge_idx]

            # 获取frame_i中的像素坐标和3D点
            h_i, w_i = frame_i.img.shape[-2:]
            h_j, w_j = frame_j.img.shape[-2:]

            # 获取像素坐标
            y_coords_i, x_coords_i = torch.meshgrid(torch.arange(h_i), torch.arange(w_i), indexing='ij')
            pixel_coords_i = torch.stack([x_coords_i.flatten(), y_coords_i.flatten()], dim=1).float().to(self.device)

            y_coords_j, x_coords_j = torch.meshgrid(torch.arange(h_j), torch.arange(w_j), indexing='ij')
            pixel_coords_j = torch.stack([x_coords_j.flatten(), y_coords_j.flatten()], dim=1).float().to(self.device)

            # 获取有效匹配的像素坐标
            valid_pixels_i = pixel_coords_i[valid_mask.view(-1)]
            valid_pixels_j = pixel_coords_j[idx_ii2jj.view(-1)][valid_mask.view(-1)]

            if len(valid_pixels_i) == 0:
                continue

            # 如果优化深度，则重新计算3D点
            if len(log_depths) > 0 and log_depths[idx_i] is not None:
                # 从深度图重新计算3D点
                depth_i = torch.exp(log_depths[idx_i]).view(h_i, w_i)
                pts3d_i = self._unproject_points(valid_pixels_i, depth_i, K)
            else:
                # 使用原始X_canon
                pts3d_i = frame_i.X_canon.view(-1, 3)[valid_mask.view(-1)]

            # 变换到frame_j的相机坐标系
            pts3d_i_world = self._transform_points(pts3d_i, poses[idx_i])
            pts3d_i_cam_j = self._transform_points_inverse(pts3d_i_world, poses[idx_j])

            # 投影到frame_j的图像平面
            pts2d_proj = self._project_points(pts3d_i_cam_j, K)

            # 获取frame_j中对应的2D点（目标点）
            pts2d_target = valid_pixels_j

            # 计算重投影误差 - 使用更robust的损失函数
            reproj_error = torch.norm(pts2d_proj - pts2d_target, dim=1)

            # 使用Huber损失来处理outliers
            huber_delta = 2.0  # 像素误差的阈值
            huber_loss = torch.where(reproj_error < huber_delta,
                                   0.5 * reproj_error ** 2,
                                   huber_delta * (reproj_error - 0.5 * huber_delta))

            # 使用置信度加权
            confidence = self.Q_ii2jj[edge_idx][valid_mask.view(-1)].squeeze(-1)
            loss = (confidence * huber_loss).sum()
            weight = confidence.sum()

            total_loss += loss
            total_weight += weight

        return total_loss / (total_weight + 1e-8)

    def _build_poses_from_params(self, quats, trans):
        """从四元数和平移参数构建位姿矩阵"""
        import roma
        import torch.nn.functional as F

        poses = []
        for i in range(len(quats)):
            # 归一化四元数
            quat_norm = F.normalize(quats[i], dim=0)

            # 转换为旋转矩阵
            R = roma.unitquat_to_rotmat(quat_norm)
            t = trans[i]

            # 构建4x4变换矩阵
            T = torch.eye(4, device=self.device, dtype=torch.float32)
            T[:3, :3] = R
            T[:3, 3] = t

            poses.append(T)

        return poses

    def _build_intrinsics_from_params(self, log_focal, pp):
        """从参数构建内参矩阵"""
        focal = torch.exp(log_focal)

        K = torch.eye(3, device=self.device, dtype=torch.float32)
        K[0, 0] = focal
        K[1, 1] = focal
        K[0, 2] = pp[0]
        K[1, 2] = pp[1]

        return K

    def _transform_points(self, points, pose):
        """使用位姿变换3D点"""
        # points: [N, 3], pose: [4, 4]
        ones = torch.ones(points.shape[0], 1, device=points.device, dtype=points.dtype)
        points_homo = torch.cat([points, ones], dim=1)  # [N, 4]

        transformed = (pose @ points_homo.T).T  # [N, 4]
        return transformed[:, :3]  # [N, 3]

    def _transform_points_inverse(self, points, pose):
        """使用位姿的逆变换3D点"""
        pose_inv = torch.inverse(pose)
        return self._transform_points(points, pose_inv)

    def _project_points(self, points_3d, K):
        """将3D点投影到2D图像平面"""
        # points_3d: [N, 3], K: [3, 3]
        # 投影: p_2d = K @ p_3d

        # 确保深度为正
        z = points_3d[:, 2:3].clamp(min=1e-6)

        # 归一化坐标
        points_norm = points_3d / z

        # 应用内参
        points_2d = (K @ points_norm.T).T

        return points_2d[:, :2]

    def _unproject_points(self, pixels, depth_map, K):
        """将2D像素坐标反投影到3D空间"""
        # pixels: [N, 2], depth_map: [H, W], K: [3, 3]

        # 获取对应像素的深度值
        x_coords = pixels[:, 0].long().clamp(0, depth_map.shape[1] - 1)
        y_coords = pixels[:, 1].long().clamp(0, depth_map.shape[0] - 1)
        depths = depth_map[y_coords, x_coords]

        # 计算归一化坐标
        K_inv = torch.inverse(K)
        pixels_homo = torch.cat([pixels, torch.ones(pixels.shape[0], 1, device=pixels.device)], dim=1)

        # 反投影
        rays = (K_inv @ pixels_homo.T).T  # [N, 3]
        points_3d = rays * depths.unsqueeze(1)

        return points_3d

    def _build_optimization_result(self, frame_ids_list, quats, trans, log_focal, pp, log_depths):
        """构建优化结果"""
        import roma
        import torch.nn.functional as F

        result = {
            'poses': [],
            'intrinsics': None,
            'depths': [],
            'frame_ids': frame_ids_list
        }

        # 构建位姿
        for i in range(len(frame_ids_list)):
            quat_norm = F.normalize(quats[i], dim=0)
            R = roma.unitquat_to_rotmat(quat_norm)
            t = trans[i]

            T = torch.eye(4, device=self.device, dtype=torch.float32)
            T[:3, :3] = R
            T[:3, 3] = t

            result['poses'].append(T)

        # 构建内参
        focal = torch.exp(log_focal)
        K = torch.eye(3, device=self.device, dtype=torch.float32)
        K[0, 0] = focal
        K[1, 1] = focal
        K[0, 2] = pp[0]
        K[1, 2] = pp[1]
        result['intrinsics'] = K

        # 构建深度
        for log_depth in log_depths:
            depth = torch.exp(log_depth)
            result['depths'].append(depth)

        return result

    def _apply_optimization_results(self, frame_ids_list, result):
        """应用优化结果到关键帧"""
        # 更新位姿
        for i, fid in enumerate(frame_ids_list):
            pose_matrix = result['poses'][i]
            self.frames[fid].T_WC = matrix_to_Sim3(pose_matrix)
            self.frames.T_WC[fid] = matrix_to_Sim3(pose_matrix.clone())

        # 更新内参
        if result['intrinsics'] is not None:
            self.K = result['intrinsics']
            self.frames.set_intrinsics(self.K)

        # 更新深度（如果需要）
        if result['depths']:
            for i, fid in enumerate(frame_ids_list):
                frame = self.frames[fid]
                if frame.X_canon is not None and i < len(result['depths']):
                    # 更新深度通道
                    frame.X_canon[:, 2] = result['depths'][i]

    def _estimate_intrinsics_after_optimization(self):
        """
        在位姿优化后自动估计相机内参
        使用快速的统计方法，无需复杂优化
        """
        import time
        t0 = time.time()
        try:
            logger.info("Fast auto-estimating camera intrinsics...")

            # 创建快速内参估计器
            estimator = FastIntrinsicsEstimator(device=self.device)

            # 获取所有关键帧
            keyframes = list(self.frames)

            # 使用自适应快速估计（基于深度或FOV）
            K, info = estimator.estimate_adaptive(keyframes)

            if K is not None:
                # 更新内参
                self.K = torch.from_numpy(K).to(self.device)
                self.frames.set_intrinsics(self.K)

                # logger.info(f"Successfully estimated intrinsics (fast method):")
                # logger.info(f"  Method: {info.get('method', 'unknown')}")
                # logger.info(f"  fx={K[0,0]:.1f}, fy={K[1,1]:.1f}")
                # logger.info(f"  cx={K[0,2]:.1f}, cy={K[1,2]:.1f}")

                # # 显示额外信息
                # if 'fov_x' in info and 'fov_y' in info:
                #     logger.info(f"  FOV: {info['fov_x']:.1f}° x {info['fov_y']:.1f}°")
                # if 'depth_median' in info:
                #     logger.info(f"  Depth median: {info['depth_median']:.2f}m")
            else:
                logger.warning("Failed to estimate intrinsics")

        except Exception as e:
            logger.error(f"Error in fast auto intrinsics estimation: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
        t1 = time.time()
        logger.info(f"Time taken for fast auto-intrinsics estimation: {t1-t0:.2f}s")

