"""
Python implementation of Gauss-Newton optimization algorithms for MASt3R-SLAM.

This module provides pure Python implementations of the solve_GN_rays and solve_GN_calib
algorithms originally implemented in CUDA C++. These implementations are designed to be
functionally equivalent to the original CUDA kernels but run on CPU using PyTorch.
"""

import torch
import math
from typing import List

class SparseLinearSystem:
    """Sparse linear system for Gauss-Newton optimization"""
    def __init__(self, size: int, device: torch.device):
        self.size = size
        self.device = device
        self.H = torch.zeros(size * 7, size * 7, device=device)
        self.g = torch.zeros(size * 7, device=device)
    
    def add_hessian_block(self, i: int, j: int, block: torch.Tensor):
        """Add 7x7 block to Hessian matrix"""
        self.H[i*7:(i+1)*7, j*7:(j+1)*7] += block
    
    def add_gradient_block(self, i: int, block: torch.Tensor):
        """Add 7x1 block to gradient vector"""
        self.g[i*7:(i+1)*7] += block
    
    def solve(self) -> torch.Tensor:
        """Solve the linear system"""
        try:
            dx = torch.linalg.solve(self.H, -self.g)
            return dx.view(-1, 7)
        except:
            # Fallback to pseudo-inverse if singular
            dx = torch.linalg.pinv(self.H) @ (-self.g)
            return dx.view(-1, 7)

class Sim3Utils:
    """Utility functions for Sim(3) operations"""
    
    @staticmethod
    def huber_weight(x: float, delta: float = 1.0) -> float:
        """Huber robust weight function"""
        abs_x = abs(x)
        if abs_x <= delta:
            return 1.0
        else:
            return delta / abs_x
    
    @staticmethod
    def act_sim3(t: torch.Tensor, q: torch.Tensor, s: torch.Tensor, X: torch.Tensor) -> torch.Tensor:
        """Apply Sim(3) transformation to point"""
        # This is a placeholder - implement actual Sim(3) action
        # For now, assume it's implemented elsewhere or use identity
        return X
    
    @staticmethod
    def compose_sim3_inverse(ti: torch.Tensor, qi: torch.Tensor, si: torch.Tensor,
                           tj: torch.Tensor, qj: torch.Tensor, sj: torch.Tensor):
        """Compose inverse Sim(3) transformation"""
        # This is a placeholder - implement actual Sim(3) composition
        # For now, return identity transformation
        return tj - ti, qj, sj

def apply_sim3_adjoint_inverse(t: torch.Tensor, q: torch.Tensor, s: torch.Tensor, 
                              xi: torch.Tensor) -> torch.Tensor:
    """Apply inverse adjoint of Sim(3) transformation"""
    # This is a placeholder - implement actual adjoint
    return xi

def retract_sim3(xi: torch.Tensor, t: torch.Tensor, q: torch.Tensor, s: torch.Tensor):
    """Retract tangent vector to Sim(3) manifold"""
    # This is a placeholder - implement actual retraction
    return t + xi[:, :3], q, s


def solve_GN_rays_python(Twc: torch.Tensor, Xs: torch.Tensor, Cs: torch.Tensor,
                        ii: torch.Tensor, jj: torch.Tensor, idx_ii2jj: torch.Tensor,
                        valid_match: torch.Tensor, Q: torch.Tensor,
                        sigma_ray: float, sigma_dist: float, C_thresh: float, Q_thresh: float,
                        max_iter: int = 10, delta_thresh: float = 1e-4, pin: int = 1) -> torch.Tensor:
    """
    Gauss-Newton optimization for ray alignment (Python implementation matching CUDA version)
    """
    device = Twc.device
    num_poses = Twc.shape[0]
    num_edges = ii.shape[0]
    
    # Extract pose parameters
    t = Twc[:, :3].clone()  # [N, 3]
    q = Twc[:, 3:7].clone()  # [N, 4]
    s = Twc[:, 7:8].clone()  # [N, 1]
    
    # Precompute inverse sigmas
    sigma_ray_inv = 1.0 / sigma_ray
    sigma_dist_inv = 1.0 / sigma_dist

    for iteration in range(max_iter):
        # Initialize sparse linear system
        linear_system = SparseLinearSystem(num_poses - pin, device)
        
        # Process each edge
        for edge_idx in range(num_edges):
            i_idx = ii[edge_idx].item()
            j_idx = jj[edge_idx].item()
            
            # Skip if either pose is pinned
            if i_idx < pin or j_idx < pin:
                continue
                
            # Get pose parameters
            ti, qi, si = t[i_idx], q[i_idx], s[i_idx]
            tj, qj, sj = t[j_idx], q[j_idx], s[j_idx]
            
            # Compute relative transformation from j to i
            tij, qij, sij = Sim3Utils.compose_sim3_inverse(ti, qi, si, tj, qj, sj)
            
            # Get point clouds for this edge
            Xi = Xs[i_idx]  # [M, 3]
            Xj = Xs[j_idx]  # [M, 3]
            Ci = Cs[i_idx]  # [M]
            Cj = Cs[j_idx]  # [M]
            
            # Initialize Hessian blocks
            H_ii = torch.zeros(7, 7, device=device)
            H_ij = torch.zeros(7, 7, device=device)
            H_jj = torch.zeros(7, 7, device=device)
            g_i = torch.zeros(7, device=device)
            g_j = torch.zeros(7, device=device)
            
            # Process each point correspondence
            for k in range(Xi.shape[0]):
                # Get validity and correspondence - match CUDA logic
                valid_match_ind = valid_match[edge_idx, k, 0].item()
                k_i = idx_ii2jj[edge_idx, k].item() if valid_match_ind else 0
                
                # Get points
                Xi_k = Xi[k_i]  # [3]
                Xj_k = Xj[k]    # [3]
                
                # Transform point from j to i coordinate system
                Xj_Ci = Sim3Utils.act_sim3(tij.unsqueeze(0), qij.unsqueeze(0), sij.unsqueeze(0), Xj_k.unsqueeze(0)).squeeze(0)
                
                # Check validity - match CUDA logic
                q_val = Q[edge_idx, k, 0].item()
                ci_val = Ci[k_i].item() if valid_match_ind else 0.0
                cj_val = Cj[k].item()
                
                valid = (valid_match_ind and q_val > Q_thresh and
                        ci_val > C_thresh and cj_val > C_thresh)
                
                if not valid:
                    continue
                
                # Normalize to unit rays
                norm_i = torch.norm(Xi_k)
                norm_j = torch.norm(Xj_Ci)
                
                if norm_i < 1e-8 or norm_j < 1e-8:
                    continue
                
                ri = Xi_k / norm_i
                rj = Xj_Ci / norm_j
                
                # Compute residuals
                err_ray = rj - ri  # [3] - ray direction error
                err_dist = norm_j - norm_i  # scalar - distance error
                
                # Compute weights with validity check
                conf_weight = q_val
                sqrt_w_ray = sigma_ray_inv * math.sqrt(conf_weight)
                sqrt_w_dist = sigma_dist_inv * math.sqrt(conf_weight)
                
                # Robust weights (Huber)
                w_ray = Sim3Utils.huber_weight(sqrt_w_ray * torch.norm(err_ray))
                w_dist = Sim3Utils.huber_weight(sqrt_w_dist * abs(err_dist))
                
                # Final weights
                w_ray = w_ray * (sqrt_w_ray ** 2)
                w_dist = w_dist * (sqrt_w_dist ** 2)

                # Compute Jacobians for ray errors
                # Jacobian of normalized point w.r.t. transformed point
                norm_j_inv = 1.0 / norm_j
                norm_j_inv3 = norm_j_inv / (norm_j ** 2)

                # dr/dP where r = P/||P|| and P = Xj_Ci
                dr_dP = torch.eye(3, device=device) * norm_j_inv - torch.outer(Xj_Ci.flatten(), Xj_Ci.flatten()).view(3, 3) * norm_j_inv3

                # Jacobian of Sim3 action w.r.t. pose parameters
                # For each ray component
                for dim in range(3):
                    # Jacobian w.r.t. pose j (local coordinates)
                    J_local = torch.zeros(7, device=device)

                    # Translation part
                    J_local[:3] = dr_dP[dim, :]

                    # Rotation part (cross product with transformed point)
                    if dim == 0:  # x component
                        J_local[4] = rj[2]   # z
                        J_local[5] = -rj[1]  # -y
                    elif dim == 1:  # y component
                        J_local[3] = -rj[2]  # -z
                        J_local[5] = rj[0]   # x
                    elif dim == 2:  # z component
                        J_local[3] = rj[1]   # y
                        J_local[4] = -rj[0]  # -x

                    # Transform to global coordinates using adjoint
                    J_global = apply_sim3_adjoint_inverse(ti, qi, si, J_local.unsqueeze(0)).squeeze(0)
                    J_i = -J_global  # Jacobian w.r.t. pose i
                    J_j = J_local    # Jacobian w.r.t. pose j

                    # Add to Hessian and gradient
                    weight = w_ray[dim]
                    error = err_ray[dim]

                    H_ii += weight * torch.outer(J_i, J_i)
                    H_ij += weight * torch.outer(J_i, J_j)
                    H_jj += weight * torch.outer(J_j, J_j)

                    g_i += weight * error * J_i
                    g_j += weight * error * J_j

                # Jacobian for distance error
                J_dist_local = torch.zeros(7, device=device)
                J_dist_local[:3] = rj  # dr_norm/dt
                J_dist_local[6] = norm_j  # dr_norm/ds

                J_dist_global = apply_sim3_adjoint_inverse(ti, qi, si, J_dist_local.unsqueeze(0)).squeeze(0)
                J_dist_i = -J_dist_global
                J_dist_j = J_dist_local

                # Add distance term to Hessian and gradient
                weight_dist = w_dist.item() if w_dist.numel() == 1 else w_dist
                error_dist = err_dist.item() if err_dist.numel() == 1 else err_dist

                H_ii += weight_dist * torch.outer(J_dist_i, J_dist_i)
                H_ij += weight_dist * torch.outer(J_dist_i, J_dist_j)
                H_jj += weight_dist * torch.outer(J_dist_j, J_dist_j)

                g_i += weight_dist * error_dist * J_dist_i
                g_j += weight_dist * error_dist * J_dist_j

            # Add to sparse linear system
            i_opt = i_idx - pin
            j_opt = j_idx - pin

            linear_system.add_hessian_block(i_opt, i_opt, H_ii)
            linear_system.add_hessian_block(i_opt, j_opt, H_ij)
            linear_system.add_hessian_block(j_opt, j_opt, H_jj)

            linear_system.add_gradient_block(i_opt, g_i)
            linear_system.add_gradient_block(j_opt, g_j)

        # Solve linear system
        dx = linear_system.solve()

        # Update poses using retraction
        for k in range(pin, num_poses):
            k_opt = k - pin
            xi = dx[k_opt]
            t_new, q_new, s_new = retract_sim3(xi.unsqueeze(0), t[k].unsqueeze(0), q[k].unsqueeze(0), s[k].unsqueeze(0))
            t[k] = t_new.squeeze(0)
            q[k] = q_new.squeeze(0)
            s[k] = s_new.squeeze(0)

        # Update Twc tensor
        Twc[:, :3] = t
        Twc[:, 3:7] = q
        Twc[:, 7:8] = s

        # Check convergence
        delta_norm = torch.norm(dx)
        if delta_norm < delta_thresh:
            break

    return dx


def solve_GN_calib_python(Twc: torch.Tensor, Xs: torch.Tensor, Cs: torch.Tensor, K: torch.Tensor,
                         ii: torch.Tensor, jj: torch.Tensor, idx_ii2jj: torch.Tensor,
                         valid_match: torch.Tensor, Q: torch.Tensor,
                         height: int, width: int, pixel_border: int, z_eps: float,
                         sigma_pixel: float, sigma_depth: float, C_thresh: float, Q_thresh: float,
                         max_iter: int = 10, delta_thresh: float = 1e-4, pin: int = 1) -> torch.Tensor:
    """
    Gauss-Newton optimization for calibrated camera (Python implementation matching CUDA version)
    """
    device = Twc.device
    num_poses = Twc.shape[0]
    num_edges = ii.shape[0]
    
    # Extract pose parameters
    t = Twc[:, :3].clone()  # [N, 3]
    q = Twc[:, 3:7].clone()  # [N, 4]
    s = Twc[:, 7:8].clone()  # [N, 1]
    
    # Extract camera intrinsics
    fx, fy, cx, cy = K[0, 0].item(), K[1, 1].item(), K[0, 2].item(), K[1, 2].item()
    
    # Precompute inverse sigmas
    sigma_pixel_inv = 1.0 / sigma_pixel
    sigma_depth_inv = 1.0 / sigma_depth

    for iteration in range(max_iter):
        # Initialize sparse linear system
        linear_system = SparseLinearSystem(num_poses - pin, device)
        
        # Process each edge
        for edge_idx in range(num_edges):
            i_idx = ii[edge_idx].item()
            j_idx = jj[edge_idx].item()
            
            # Skip if either pose is pinned
            if i_idx < pin or j_idx < pin:
                continue
                
            # Get pose parameters
            ti, qi, si = t[i_idx], q[i_idx], s[i_idx]
            tj, qj, sj = t[j_idx], q[j_idx], s[j_idx]
            
            # Compute relative transformation from j to i
            tij, qij, sij = Sim3Utils.compose_sim3_inverse(ti, qi, si, tj, qj, sj)
            
            # Get point clouds for this edge
            Xi = Xs[i_idx]  # [M, 3]
            Xj = Xs[j_idx]  # [M, 3]
            Ci = Cs[i_idx]  # [M]
            Cj = Cs[j_idx]  # [M]
            
            # Initialize Hessian blocks
            H_ii = torch.zeros(7, 7, device=device)
            H_ij = torch.zeros(7, 7, device=device)
            H_jj = torch.zeros(7, 7, device=device)
            g_i = torch.zeros(7, device=device)
            g_j = torch.zeros(7, device=device)
            
            # Process each point correspondence
            for k in range(Xi.shape[0]):
                # Get validity and correspondence - match CUDA logic
                valid_match_ind = valid_match[edge_idx, k, 0].item()
                k_i = idx_ii2jj[edge_idx, k].item() if valid_match_ind else 0
                
                # Get points
                Xi_k = Xi[k_i]  # [3]
                Xj_k = Xj[k]    # [3]
                
                # Transform point from j to i coordinate system
                Xj_Ci = Sim3Utils.act_sim3(tij.unsqueeze(0), qij.unsqueeze(0), sij.unsqueeze(0), Xj_k.unsqueeze(0)).squeeze(0)
                
                # Check depth validity
                valid_z = Xj_Ci[2] > z_eps
                if not valid_z:
                    continue
                
                # Compute target pixel coordinates (project Xi to camera i)
                z_inv_target = 1.0 / Xi_k[2]
                u_target = fx * Xi_k[0] * z_inv_target + cx
                v_target = fy * Xi_k[1] * z_inv_target + cy
                
                # Project point to image
                z_inv = 1.0 / Xj_Ci[2]
                x_div_z = Xj_Ci[0] * z_inv
                y_div_z = Xj_Ci[1] * z_inv
                u = fx * x_div_z + cx
                v = fy * y_div_z + cy
                
                # Check validity - match CUDA logic
                q_val = Q[edge_idx, k, 0].item()
                ci_val = Ci[k_i].item() if valid_match_ind else 0.0
                cj_val = Cj[k].item()
                
                # Check if projection is within image bounds
                valid_u = (u > pixel_border) and (u < width - 1 - pixel_border)
                valid_v = (v > pixel_border) and (v < height - 1 - pixel_border)
                
                valid = (valid_match_ind and q_val > Q_thresh and
                        ci_val > C_thresh and cj_val > C_thresh and
                        valid_u and valid_v and valid_z)
                
                if not valid:
                    continue
                
                # Compute residuals
                err_u = u - u_target
                err_v = v - v_target
                err_depth = math.log(Xj_Ci[2]) - math.log(Xi_k[2])  # log-depth error
                
                # Compute weights with validity check
                conf_weight = q_val
                sqrt_w_pixel = sigma_pixel_inv * math.sqrt(conf_weight)
                sqrt_w_depth = sigma_depth_inv * math.sqrt(conf_weight)
                
                # Robust weights (Huber)
                w_u = Sim3Utils.huber_weight(sqrt_w_pixel * abs(err_u))
                w_v = Sim3Utils.huber_weight(sqrt_w_pixel * abs(err_v))
                w_depth = Sim3Utils.huber_weight(sqrt_w_depth * abs(err_depth))
                
                # Final weights
                w_u = w_u * (sqrt_w_pixel ** 2)
                w_v = w_v * (sqrt_w_pixel ** 2)
                w_depth = w_depth * (sqrt_w_depth ** 2)

                # Compute Jacobians for ray errors
                # Jacobian of normalized point w.r.t. transformed point
                norm_j_inv = 1.0 / norm_j
                norm_j_inv3 = norm_j_inv / (norm_j ** 2)

                # dr/dP where r = P/||P|| and P = Xj_Ci
                dr_dP = torch.eye(3, device=device) * norm_j_inv - torch.outer(Xj_Ci.flatten(), Xj_Ci.flatten()).view(3, 3) * norm_j_inv3

                # Jacobian of Sim3 action w.r.t. pose parameters
                # For each ray component
                for dim in range(3):
                    # Jacobian w.r.t. pose j (local coordinates)
                    J_local = torch.zeros(7, device=device)

                    # Translation part
                    J_local[:3] = dr_dP[dim, :]

                    # Rotation part (cross product with transformed point)
                    if dim == 0:  # x component
                        J_local[4] = rj[2]   # z
                        J_local[5] = -rj[1]  # -y
                    elif dim == 1:  # y component
                        J_local[3] = -rj[2]  # -z
                        J_local[5] = rj[0]   # x
                    elif dim == 2:  # z component
                        J_local[3] = rj[1]   # y
                        J_local[4] = -rj[0]  # -x

                    # Transform to global coordinates using adjoint
                    J_global = apply_sim3_adjoint_inverse(ti, qi, si, J_local.unsqueeze(0)).squeeze(0)
                    J_i = -J_global  # Jacobian w.r.t. pose i
                    J_j = J_local    # Jacobian w.r.t. pose j

                    # Add to Hessian and gradient
                    weight = w_ray[dim]
                    error = err_ray[dim]

                    H_ii += weight * torch.outer(J_i, J_i)
                    H_ij += weight * torch.outer(J_i, J_j)
                    H_jj += weight * torch.outer(J_j, J_j)

                    g_i += weight * error * J_i
                    g_j += weight * error * J_j

                # Jacobian for distance error
                J_dist_local = torch.zeros(7, device=device)
                J_dist_local[:3] = rj  # dr_norm/dt
                J_dist_local[6] = norm_j  # dr_norm/ds

                J_dist_global = apply_sim3_adjoint_inverse(ti, qi, si, J_dist_local.unsqueeze(0)).squeeze(0)
                J_dist_i = -J_dist_global
                J_dist_j = J_dist_local

                # Add distance term to Hessian and gradient
                weight_dist = w_dist.item() if w_dist.numel() == 1 else w_dist
                error_dist = err_dist.item() if err_dist.numel() == 1 else err_dist

                H_ii += weight_dist * torch.outer(J_dist_i, J_dist_i)
                H_ij += weight_dist * torch.outer(J_dist_i, J_dist_j)
                H_jj += weight_dist * torch.outer(J_dist_j, J_dist_j)

                g_i += weight_dist * error_dist * J_dist_i
                g_j += weight_dist * error_dist * J_dist_j

            # Add to sparse linear system
            i_opt = i_idx - pin
            j_opt = j_idx - pin

            linear_system.add_hessian_block(i_opt, i_opt, H_ii)
            linear_system.add_hessian_block(i_opt, j_opt, H_ij)
            linear_system.add_hessian_block(j_opt, j_opt, H_jj)

            linear_system.add_gradient_block(i_opt, g_i)
            linear_system.add_gradient_block(j_opt, g_j)

        # Solve linear system
        dx = linear_system.solve()

        # Update poses using retraction
        for k in range(pin, num_poses):
            k_opt = k - pin
            xi = dx[k_opt]
            t_new, q_new, s_new = retract_sim3(xi.unsqueeze(0), t[k].unsqueeze(0), q[k].unsqueeze(0), s[k].unsqueeze(0))
            t[k] = t_new.squeeze(0)
            q[k] = q_new.squeeze(0)
            s[k] = s_new.squeeze(0)

        # Update Twc tensor
        Twc[:, :3] = t
        Twc[:, 3:7] = q
        Twc[:, 7:8] = s

        # Check convergence
        delta_norm = torch.norm(dx)
        if delta_norm < delta_thresh:
            break

    return dx


# Wrapper functions to match the original interface
def gauss_newton_rays_python(Twc: torch.Tensor, Xs: torch.Tensor, Cs: torch.Tensor,
                            ii: torch.Tensor, jj: torch.Tensor, idx_ii2jj: torch.Tensor,
                            valid_match: torch.Tensor, Q: torch.Tensor,
                            sigma_ray: float, sigma_dist: float, C_thresh: float, Q_thresh: float,
                            max_iter: int, delta_thresh: float) -> List[torch.Tensor]:
    """
    Wrapper function that matches the original CUDA interface for ray alignment.

    Returns:
        List containing the update vector dx for debugging purposes
    """
    dx = solve_GN_rays_python(
        Twc, Xs, Cs, ii, jj, idx_ii2jj, valid_match, Q,
        sigma_ray, sigma_dist, C_thresh, Q_thresh, max_iter, delta_thresh
    )
    return [dx]


def gauss_newton_calib_python(Twc: torch.Tensor, Xs: torch.Tensor, Cs: torch.Tensor, K: torch.Tensor,
                             ii: torch.Tensor, jj: torch.Tensor, idx_ii2jj: torch.Tensor,
                             valid_match: torch.Tensor, Q: torch.Tensor,
                             height: int, width: int, pixel_border: int, z_eps: float,
                             sigma_pixel: float, sigma_depth: float, C_thresh: float, Q_thresh: float,
                             max_iter: int, delta_thresh: float) -> List[torch.Tensor]:
    """
    Wrapper function that matches the original CUDA interface for calibrated camera optimization.

    Returns:
        List containing the update vector dx for debugging purposes
    """
    dx = solve_GN_calib_python(
        Twc, Xs, Cs, K, ii, jj, idx_ii2jj, valid_match, Q,
        height, width, pixel_border, z_eps, sigma_pixel, sigma_depth,
        C_thresh, Q_thresh, max_iter, delta_thresh
    )
    return [dx]
